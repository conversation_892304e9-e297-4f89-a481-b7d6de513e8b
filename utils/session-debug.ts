'use client';

import { type SessionInfo } from './session-manager';

/**
 * Debug utility for simulating different session states
 * Only available in development mode
 */

export interface DebugSessionState {
  mode: 'normal' | 'warning' | 'critical' | 'expired';
  customTimeLeft?: number; // in milliseconds
}

let debugState: DebugSessionState = { mode: 'normal' };

/**
 * Set debug session state (development only)
 */
export function setDebugSessionState(state: DebugSessionState) {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Debug session state can only be set in development mode');
    return;
  }
  
  debugState = state;
}

/**
 * Get current debug state
 */
export function getDebugSessionState(): DebugSessionState {
  return debugState;
}

/**
 * Reset debug state to normal
 */
export function resetDebugSessionState() {
  debugState = { mode: 'normal' };
}

/**
 * Apply debug modifications to session info
 */
export function applyDebugSessionState(sessionInfo: SessionInfo): SessionInfo {
  if (process.env.NODE_ENV !== 'development' || debugState.mode === 'normal') {
    return sessionInfo;
  }

  const now = Date.now();
  
  switch (debugState.mode) {
    case 'warning':
      // Simulate session expiring in 4 minutes (warning threshold is 5 minutes)
      const warningTimeLeft = debugState.customTimeLeft || (4 * 60 * 1000);
      return {
        ...sessionInfo,
        isValid: true,
        isExpired: false,
        isExpiringSoon: true,
        timeUntilExpiry: warningTimeLeft,
        expiresAt: (now + warningTimeLeft) / 1000,
        securityLevel: 'warning',
        lastValidated: now
      };
      
    case 'critical':
      // Simulate critical security issue
      return {
        ...sessionInfo,
        isValid: false,
        isExpired: false,
        isExpiringSoon: false,
        timeUntilExpiry: null,
        expiresAt: null,
        securityLevel: 'critical',
        error: 'Security breach detected - please sign in again',
        lastValidated: now
      };
      
    case 'expired':
      // Simulate expired session
      return {
        ...sessionInfo,
        isValid: false,
        isExpired: true,
        isExpiringSoon: false,
        timeUntilExpiry: 0,
        expiresAt: (now - 1000) / 1000, // Expired 1 second ago
        securityLevel: 'critical',
        error: 'Session has expired',
        lastValidated: now
      };
      
    default:
      return sessionInfo;
  }
}

/**
 * Debug helper functions for easy testing
 */
export const sessionDebug = {
  /**
   * Simulate warning state (session expiring in 4 minutes)
   */
  warning: (timeLeftMinutes: number = 4) => {
    setDebugSessionState({ 
      mode: 'warning', 
      customTimeLeft: timeLeftMinutes * 60 * 1000 
    });
  },
  
  /**
   * Simulate critical security issue
   */
  critical: () => {
    setDebugSessionState({ mode: 'critical' });
  },
  
  /**
   * Simulate expired session
   */
  expired: () => {
    setDebugSessionState({ mode: 'expired' });
  },
  
  /**
   * Reset to normal state
   */
  normal: () => {
    resetDebugSessionState();
  },
  
  /**
   * Simulate session expiring in 30 seconds (urgent warning)
   */
  urgent: () => {
    setDebugSessionState({ 
      mode: 'warning', 
      customTimeLeft: 30 * 1000 
    });
  },
  
  /**
   * Simulate session expiring in 1 minute (critical warning)
   */
  criticalWarning: () => {
    setDebugSessionState({ 
      mode: 'warning', 
      customTimeLeft: 60 * 1000 
    });
  }
};

