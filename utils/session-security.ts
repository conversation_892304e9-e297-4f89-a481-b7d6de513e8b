/**
 * Enhanced Session Security Utilities
 * 
 * Provides comprehensive session validation and security monitoring
 * to prevent session hijacking, fixation, and unauthorized access.
 */

import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export interface SessionValidationResult {
  isValid: boolean;
  reason?: string;
  shouldRefresh?: boolean;
  session?: any;
}

export interface SessionFingerprint {
  userAgent: string;
  acceptLanguage: string;
  acceptEncoding: string;
  ipAddress: string;
}

/**
 * Generate a session fingerprint based on request headers
 */
export function generateSessionFingerprint(request: NextRequest): string {
  const userAgent = request.headers.get('user-agent') || '';
  const acceptLanguage = request.headers.get('accept-language') || '';
  const acceptEncoding = request.headers.get('accept-encoding') || '';
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   request.ip || 
                   'unknown';

  // Create a simple hash of the fingerprint components
  const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}|${ipAddress}`;
  
  // Simple hash function (in production, use a proper crypto hash)
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return hash.toString(36);
}

/**
 * Validate session integrity with comprehensive security checks
 */
export async function validateSessionIntegrity(request: NextRequest): Promise<SessionValidationResult> {
  try {
    const supabase = createClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session) {
      return { isValid: false, reason: 'No valid session' };
    }
    
    // Check session expiration with buffer
    const expirationBuffer = 5 * 60 * 1000; // 5 minutes
    const expiresAt = new Date(session.expires_at! * 1000);
    const now = new Date();
    
    if (expiresAt.getTime() - now.getTime() < expirationBuffer) {
      return { 
        isValid: false, 
        reason: 'Session expiring soon', 
        shouldRefresh: true 
      };
    }
    
    // Validate session fingerprint (basic implementation)
    const currentFingerprint = generateSessionFingerprint(request);
    const storedFingerprint = session.user.user_metadata?.session_fingerprint;
    
    // For new sessions, store the fingerprint
    if (!storedFingerprint) {
      try {
        await supabase.auth.updateUser({
          data: { session_fingerprint: currentFingerprint }
        });
      } catch (error) {
        // If we can't update the fingerprint, continue but log the issue
        console.warn('Could not store session fingerprint:', error);
      }
    } else if (storedFingerprint !== currentFingerprint) {
      // Fingerprint mismatch - potential session hijacking
      return { 
        isValid: false, 
        reason: 'Session fingerprint mismatch - potential security threat' 
      };
    }
    
    // Check for suspicious session patterns
    const sessionAge = now.getTime() - new Date(session.user.created_at).getTime();
    const maxSessionAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (sessionAge > maxSessionAge) {
      return { 
        isValid: false, 
        reason: 'Session too old - please re-authenticate' 
      };
    }
    
    return { isValid: true, session };
  } catch (error) {
    return { 
      isValid: false, 
      reason: 'Session validation error' 
    };
  }
}

/**
 * Enhanced session monitoring for security events
 */
export class SessionSecurityMonitor {
  private static instance: SessionSecurityMonitor;
  private securityEvents: Array<{
    timestamp: Date;
    event: string;
    details: any;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }> = [];

  static getInstance(): SessionSecurityMonitor {
    if (!SessionSecurityMonitor.instance) {
      SessionSecurityMonitor.instance = new SessionSecurityMonitor();
    }
    return SessionSecurityMonitor.instance;
  }

  /**
   * Log a security event
   */
  logSecurityEvent(
    event: string, 
    details: any, 
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): void {
    const securityEvent = {
      timestamp: new Date(),
      event,
      details,
      severity
    };

    this.securityEvents.push(securityEvent);

    // Keep only the last 1000 events to prevent memory issues
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // In production, you would send this to a security monitoring service
    // Example: sendToSecurityService(securityEvent);
  }

  /**
   * Get recent security events
   */
  getRecentEvents(limit: number = 50): Array<any> {
    return this.securityEvents
      .slice(-limit)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Check for suspicious patterns
   */
  detectSuspiciousActivity(): {
    hasSuspiciousActivity: boolean;
    patterns: string[];
  } {
    const recentEvents = this.getRecentEvents(100);
    const patterns: string[] = [];

    // Check for multiple failed authentication attempts
    const failedAttempts = recentEvents.filter(
      event => event.event.includes('authentication_failed') && 
      event.timestamp.getTime() > Date.now() - (15 * 60 * 1000) // Last 15 minutes
    );

    if (failedAttempts.length > 5) {
      patterns.push('Multiple failed authentication attempts detected');
    }

    // Check for session fingerprint mismatches
    const fingerprintMismatches = recentEvents.filter(
      event => event.event.includes('fingerprint_mismatch') &&
      event.timestamp.getTime() > Date.now() - (60 * 60 * 1000) // Last hour
    );

    if (fingerprintMismatches.length > 2) {
      patterns.push('Multiple session fingerprint mismatches detected');
    }

    // Check for rapid session creation/destruction
    const sessionEvents = recentEvents.filter(
      event => (event.event.includes('session_created') || event.event.includes('session_destroyed')) &&
      event.timestamp.getTime() > Date.now() - (30 * 60 * 1000) // Last 30 minutes
    );

    if (sessionEvents.length > 10) {
      patterns.push('Rapid session creation/destruction detected');
    }

    return {
      hasSuspiciousActivity: patterns.length > 0,
      patterns
    };
  }
}

/**
 * Middleware helper for session security validation
 */
export async function validateSessionSecurity(request: NextRequest): Promise<{
  isValid: boolean;
  response?: Response;
  securityIssues?: string[];
}> {
  const monitor = SessionSecurityMonitor.getInstance();
  
  try {
    // Validate session integrity
    const validation = await validateSessionIntegrity(request);
    
    if (!validation.isValid) {
      monitor.logSecurityEvent(
        'session_validation_failed',
        { reason: validation.reason, path: request.nextUrl.pathname },
        'high'
      );

      return {
        isValid: false,
        securityIssues: [validation.reason || 'Session validation failed']
      };
    }

    // Check for suspicious activity patterns
    const suspiciousActivity = monitor.detectSuspiciousActivity();
    
    if (suspiciousActivity.hasSuspiciousActivity) {
      monitor.logSecurityEvent(
        'suspicious_activity_detected',
        { patterns: suspiciousActivity.patterns, path: request.nextUrl.pathname },
        'critical'
      );

      return {
        isValid: false,
        securityIssues: suspiciousActivity.patterns
      };
    }

    // Log successful validation
    monitor.logSecurityEvent(
      'session_validation_success',
      { path: request.nextUrl.pathname },
      'low'
    );

    return { isValid: true };
  } catch (error) {
    monitor.logSecurityEvent(
      'session_security_error',
      { error: error instanceof Error ? error.message : 'Unknown error', path: request.nextUrl.pathname },
      'high'
    );

    return {
      isValid: false,
      securityIssues: ['Session security validation error']
    };
  }
}
