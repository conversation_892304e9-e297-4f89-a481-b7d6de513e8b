{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@ai-sdk/google": "^1.2.14", "@ai-sdk/openai": "^1.3.20", "@ai-sdk/react": "^1.2.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@excalidraw/excalidraw": "^0.17.6", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@google-cloud/speech": "^7.0.1", "@google/generative-ai": "^0.24.0", "@hocuspocus/provider": "^2.13.6", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^4.1.5", "@remixicon/react": "^4.6.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tabler/icons-react": "^3.19.0", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-list-keymap": "^2.11.7", "@tiptap/extension-paragraph": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-typography": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/bcryptjs": "^2.4.6", "@types/dagre": "^0.7.52", "@types/file-saver": "^2.0.7", "@types/react-rnd": "^7.4.4", "@types/react-signature-canvas": "^1.0.6", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "@xyflow/react": "^12.3.6", "ai": "^4.3.11", "apexcharts": "^4.1.0", "autoprefixer": "10.4.17", "bcryptjs": "^3.0.2", "caniuse-lite": "^1.0.30001712", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dagre": "^0.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dom-to-image": "^2.6.0", "file-saver": "^2.0.5", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^11.11.9", "geist": "^1.2.1", "groq-sdk": "^0.7.0", "html2canvas": "^1.4.1", "js-file-download": "^0.4.12", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "lodash": "^4.17.21", "lowlight": "^3.1.0", "lucide-react": "^0.436.0", "next": "14.2.16", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "openai": "^4.91.1", "postcss-nested": "^6.2.0", "postgres": "^3.4.5", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-colorful": "^5.6.1", "react-component-export-image": "^1.0.6", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-grid-layout": "^1.4.4", "react-hook-form": "^7.54.1", "react-markdown": "^10.1.0", "react-rnd": "^10.4.13", "react-signature-canvas": "^1.0.6", "react-to-print": "^3.0.2", "recharts": "^2.15.3", "recharts-to-png": "^2.3.2", "regression": "^2.0.1", "resend": "^4.5.1", "save-svg-as-png": "^1.4.17", "shadcn": "^2.1.6", "sonner": "^1.7.4", "supabase": "^2.15.8", "swapy": "^0.4.2", "tailwind-variants": "^1.0.0", "tippy.js": "^6.3.7", "tiptap": "^0.15.0", "tiptap-markdown": "^0.8.10", "tiptap-slash-react": "^1.1.4", "uuid": "^11.1.0", "xlsx": "^0.18.5", "yarn": "^1.22.22", "yjs": "^13.6.19", "zod": "^3.25.23"}, "devDependencies": {"@types/dom-to-image": "^2.6.7", "@types/lodash": "^4.17.10", "@types/node": "20.10.6", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@types/react-grid-layout": "^1.3.5", "@types/react-table": "^7.7.20", "@types/regression": "^2.0.6", "next-superjson-plugin": "^0.6.3", "postcss": "8.4.33", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3"}, "resolutions": {"next": "14.2.16"}, "overrides": {"next": "14.2.16", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "packageManager": "yarn@1.22.19"}