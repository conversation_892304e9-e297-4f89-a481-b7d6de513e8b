"use client";

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import ReactSignatureCanvas from 'react-signature-canvas';

// Type assertion to fix React 19 RC compatibility issue
const SignaturePad = ReactSignatureCanvas as any;
import { toast } from 'sonner';
import { Download, Lock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import * as bcrypt from 'bcryptjs';
import FormLogo from '@/components/FormLogo';

interface TPAData {
  password_hash: any;
  id: number;
  household_id: number;
  client_name: string;
  address: string; // Client's address
  status: string;
  signature: string;
  signature_date: string;
  ip_address: string;
  adviser_name?: string; // Optional: Adviser's name
  adviser_org_name?: string; // Optional: Adviser's organization name
  adviser_address?: string; // Optional: Adviser's address
}

export default function TPAForm({ params }: { params: { token: string } }) {
  // Authentication and password protection states
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [passwordVerified, setPasswordVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // Form data states - only used after password verification
  const [tpaData, setTPAData] = useState<TPAData | null>(null);
  const [address, setAddress] = useState('');
  const [signaturePad, setSignaturePad] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [memberName, setMemberName] = useState<string | null>(null);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const router = useRouter();

  // First useEffect - only check for password protection
  useEffect(() => {
    const checkPasswordProtection = async () => {
      setInitialLoading(true);
      const supabase = createClient();

      // Check if user is logged in
      const { data: { user } } = await supabase.auth.getUser();
      setIsLoggedIn(!!user);

      // Only check for password protection initially
      const { data, error } = await supabase
        .from('tpa_tokens')
        .select('password_hash, status')
        .eq('token', params.token)
        .single();

      if (error) {
        toast.error('Invalid or expired link');
        setInitialLoading(false);
        return;
      }

      // Check if the form is password protected
      if (data.password_hash) {
        setIsPasswordProtected(true);
        // If user is logged in, bypass password protection
        if (user) {
          setPasswordVerified(true);
        }
      } else {
        // No password protection, consider it verified
        setPasswordVerified(true);
      }

      setInitialLoading(false);
    };

    checkPasswordProtection();
  }, [params.token]);

  // State for adviser's organization ID
  const [adviserOrgId, setAdviserOrgId] = useState<string | null>(null);

  // Second useEffect - only runs after password verification
  useEffect(() => {
    // Only fetch form data if password is verified or user is logged in
    if (!passwordVerified && !isLoggedIn) return;

    const fetchTPAData = async () => {
      const supabase = createClient();

      const { data, error } = await supabase
        .from('tpa_tokens')
        .select('*, households:household_id(householdName, members)')
        .eq('token', params.token)
        .single();

      if (error || !data) {
        toast.error('Invalid or expired link');
        return;
      }

      // If the form is completed, show it in read-only mode
      if (data.status === 'completed') {
        setIsReadOnly(true);
        if (!isLoggedIn) {
          toast('This form has already been submitted and cannot be modified');
        }
      } else if (data.status !== 'pending') {
        // If the form is not pending and the user is not logged in, show error
        if (!isLoggedIn) {
          toast.error('This form is no longer accessible');
          return;
        }
        // For logged in users, show in read-only mode
        setIsReadOnly(true);
      }

      // Get member name if member_id is present
      if (data.member_id && data.households?.members) {
        const memberKey = `name${data.member_id}`;
        const memberName = data.households.members[memberKey];
        if (memberName) {
          setMemberName(memberName);
        }
      }

      setTPAData(data);
      setAddress(data.address || '');

      // Get the creator's profile information
      if (data.created_by) {
        const { data: creatorProfile, error: creatorError } = await supabase
          .from('profiles')
          .select('org_id')
          .eq('user_id', data.created_by)
          .single();

        if (!creatorError && creatorProfile) {
          setAdviserOrgId(creatorProfile.org_id);
        }
      }
    };

    fetchTPAData();
  }, [params.token, passwordVerified, isLoggedIn]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!signaturePad || !tpaData || !address.trim()) {
      toast.error('Please provide your address and signature');
      return;
    }

    setIsSubmitting(true);

    try {
      const supabase = createClient();
      const signatureData = signaturePad.toDataURL();
      const ipAddress = await fetch('https://api.ipify.org?format=json')
        .then(res => res.json())
        .then(data => data.ip);

      const { error } = await supabase
        .from('tpa_tokens')
        .update({
          status: 'completed',
          address,
          signature: signatureData,
          signature_date: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          ip_address: ipAddress
        })
        .eq('id', tpaData.id);

      if (error) throw error;

      // Fetch the token data to get the created_by user ID
      const { data: tokenData, error: tokenError } = await supabase
        .from('tpa_tokens')
        .select('created_by, household_id')
        .eq('id', tpaData.id)
        .single();

      if (tokenError) {
        console.error('Error fetching token data:', tokenError);
      } else if (tokenData) {
        // Fetch household name
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('householdName')
          .eq('id', tokenData.household_id)
          .single();

        const householdName = householdError ? 'Unknown Household' : householdData?.householdName;

        // Create a notification for the user who created the TPA form
        await supabase.from('notifications').insert({
          user_id: tokenData.created_by,
          content: `TPA form for ${householdName} has been completed`,
          type: 'tpa_form_submission',
          link: `/protected/households/household/${tokenData.household_id}`,
          created_at: new Date().toISOString()
        });
      }

      toast.success('Form submitted successfully');
      router.push('/submission-success');
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Error submitting form');
    } finally {
      setIsSubmitting(false);
    }
  };

  const verifyPassword = async () => {
    setPasswordError(null);

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('tpa_tokens')
        .select('password_hash')
        .eq('token', params.token)
        .single();

      if (error || !data || !data.password_hash) {
        setPasswordError('Could not verify password. Please try again.');
        return;
      }

      // Compare the entered password with the stored hash
      const isValid = await bcrypt.compare(password, data.password_hash);

      if (isValid) {
        setPasswordVerified(true);
      } else {
        setPasswordError('Incorrect password. Please try again.');
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setPasswordError('An error occurred while verifying the password.');
    }
  };

  // Initial loading state - checking if password protection is needed
  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto mb-4"></div>
          <p className="text-xl text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  // Show password prompt if the form is password protected and not yet verified
  if (isPasswordProtected && !passwordVerified && !isLoggedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Password Protected Form
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                This form is password protected. Please enter the password to continue.
              </p>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                />
                {passwordError && (
                  <p className="text-sm text-red-500">{passwordError}</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={verifyPassword} className="w-full">
              Continue
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!tpaData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto mb-4"></div>
          <p className="text-xl text-gray-700">Loading Third Party Authority Form...</p>
        </div>
      </div>
    );
  }

  const isCompleted = tpaData.status === 'completed' || isReadOnly;

  return (
    <div className="max-w-4xl mx-auto p-8">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Third Party Authority</h2>
          <FormLogo orgId={adviserOrgId || undefined} />
        </div>
        <h2 className="text-xl font-semibold mb-4">Client Information</h2>
        {memberName ? (
          <p className="mb-4">Client Name: {memberName}</p>
        ) : (
          <p className="mb-4">Client Name: {tpaData.client_name}</p>
        )}

        {isCompleted ? (
          <p className="mb-6">Address: {tpaData.address}</p>
        ) : (
          <div className="mb-6">
            <Label htmlFor="address">Your Address</Label>
            <Textarea
              id="address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter your full address"
              className="mt-1"
              required
            />
          </div>
        )}

        <div className="prose max-w-none mb-8">
          <h3 className="text-lg font-semibold mb-4">Authority Details</h3>
          <div className="space-y-4">
            <p>I/We, {tpaData.client_name}, hereby authorize:</p>

            {tpaData.adviser_name && (
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="font-semibold">{tpaData.adviser_name}</p>
                <p>Financial Adviser</p>
                {tpaData.adviser_org_name && <p>{tpaData.adviser_org_name}</p>}
                {tpaData.adviser_address && (
                  <pre className="whitespace-pre-wrap font-sans text-sm mt-1">
                    {tpaData.adviser_address}
                  </pre>
                )}
              </div>
            )}

            <p className="text-justify">
              To act as my/our agent to obtain information about my/our investments,
              superannuation, insurance policies, and any other financial products held
              with various financial institutions. This authority permits the above-named
              adviser to:</p>

            <ul className="list-disc pl-6">
              <li>Request and receive information about my/our accounts and policies</li>
              <li>Access statements and transaction histories</li>
              <li>Discuss my/our financial affairs with the relevant institutions</li>
              <li>Receive copies of documentation relating to my/our accounts</li>
            </ul>

            <p className="text-justify">
              I/We understand that this authority does not permit the adviser to:
            </p>

            <ul className="list-disc pl-6">
              <li>Operate any accounts or make changes to investments</li>
              <li>Withdraw or transfer funds</li>
              <li>Make changes to policy or account details</li>
              <li>Close or open new accounts</li>
            </ul>

            <p className="text-justify">
              This authority remains valid until revoked by me/us in writing.
            </p>
          </div>
        </div>

        {isCompleted ? (
          <div className="space-y-4">
            <div className="border rounded p-4">
              <h4 className="font-semibold mb-2">Signed By</h4>
              <img src={tpaData.signature} alt="Signature" className="max-w-md h-40 object-contain" />
              <div className="text-xs text-gray-500 mt-2">
                <p>Signed on: {new Date(tpaData.signature_date).toLocaleString()}</p>
                <p>IP Address: {tpaData.ip_address}</p>
              </div>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-semibold">Signature</h4>
              <div className="border rounded-md bg-white">
                <SignaturePad
                  ref={(ref: any) => setSignaturePad(ref)}
                  canvasProps={{
                    className: "w-full h-48"
                  }}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => signaturePad?.clear()}
                >
                  Clear Signature
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </Button>
              </div>
            </div>
          </form>
        )}
      </div>

    {isLoggedIn && (
      <div className="max-w-4xl mx-auto p-4 mb-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="text-sm text-gray-600 mb-4">
            <p>You are viewing this form as a logged-in user. You can download this form as a PDF using the button below.</p>
          </div>
          <Button
            onClick={() => window.print()}
            className="flex items-center gap-2"
          >
            <Download size={16} />
            Download as PDF
          </Button>
        </div>
      </div>
    )}
    </div>
  );
}
