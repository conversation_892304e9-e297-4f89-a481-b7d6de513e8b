# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ai-sdk/google@^1.2.14":
  version "1.2.14"
  resolved "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.14.tgz"
  integrity sha512-r3FSyyWl0KVjUlKn5o+vMl+Nk8Z/mV6xrqW+49g7fMoRVr/wkRxJZtHorrdDGRreCJubZyAk8ziSQSLpgv2H6w==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"

"@ai-sdk/openai@^1.3.20":
  version "1.3.20"
  resolved "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.20.tgz"
  integrity sha512-/DflUy7ROG9k6n6YTXMBFPbujBKnbGY58f3CwvicLvDar9nDAloVnUWd3LUoOxpSVnX8vtQ7ngxF52SLWO6RwQ==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"

"@ai-sdk/provider-utils@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.7.tgz"
  integrity sha512-kM0xS3GWg3aMChh9zfeM+80vEZfXzR3JEUBdycZLtbRZ2TRT8xOj3WodGHPb06sUK5yD7pAXC/P7ctsi2fvUGQ==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    nanoid "^3.3.8"
    secure-json-parse "^2.7.0"

"@ai-sdk/provider@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@ai-sdk/provider/-/provider-1.1.3.tgz"
  integrity sha512-qZMxYJ0qqX/RfnuIaab+zp8UAeJn/ygXXAffR5I4N0n1IrvA6qBsjc8hXLmBiMV2zoXlifkacF7sEFnYnjBcqg==
  dependencies:
    json-schema "^0.4.0"

"@ai-sdk/react@1.2.10", "@ai-sdk/react@^1.2.9":
  version "1.2.10"
  resolved "https://registry.npmjs.org/@ai-sdk/react/-/react-1.2.10.tgz"
  integrity sha512-iUZfApc6aftVT7f41y9b1NPk0dZFt9vRR0/gkZsKdP56ShcKtuTu44BkjtWdrBs7fcTbN2BQZtDao1AY1GxzsQ==
  dependencies:
    "@ai-sdk/provider-utils" "2.2.7"
    "@ai-sdk/ui-utils" "1.2.9"
    swr "^2.2.5"
    throttleit "2.1.0"

"@ai-sdk/ui-utils@1.2.9":
  version "1.2.9"
  resolved "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.9.tgz"
  integrity sha512-cbiLzgXDv3+460f61UVSykn3XdKOS+SHs/EANw+pdOQKwn8JN7rZJL/ggPyMuZ7D9lO3oWOfOJ1QS+9uClfVug==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"
    zod-to-json-schema "^3.24.1"

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/ni@^23.2.0":
  version "23.3.1"
  resolved "https://registry.npmjs.org/@antfu/ni/-/ni-23.3.1.tgz"
  integrity sha512-C90iyzm/jLV7Lomv2UzwWUzRv9WZr1oRsFRKsX5HjQL4EXrbi9H/RtBkjCP+NF+ABZXUKpAa4F1dkoTaea4zHg==

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.26.8":
  version "7.26.8"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz"
  integrity sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==

"@babel/core@^7.22.1":
  version "7.26.10"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz"
  integrity sha512-vMqyb7XCDMPvJFFOaT9kxtiRh42GwlZEg1/uIgtZshS5a/8OaduUfCi7kynKgc3Tw/6Uo2D+db9qBttghhmxwQ==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.10"
    "@babel/helper-compilation-targets" "^7.26.5"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.10"
    "@babel/parser" "^7.26.10"
    "@babel/template" "^7.26.9"
    "@babel/traverse" "^7.26.10"
    "@babel/types" "^7.26.10"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.26.10", "@babel/generator@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.27.0.tgz"
  integrity sha512-VybsKvpiN1gU1sdMZIp7FcqphVVKEwcuj02x73uvcHE0PTihx1nlBcowYWhDwjpoAXRv43+gDzyggGnn1XZhVw==
  dependencies:
    "@babel/parser" "^7.27.0"
    "@babel/types" "^7.27.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.26.5":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz"
  integrity sha512-LVk7fbXml0H2xH34dFzKQ7TDZ2G4/rVTOrq9V+icbbadjbVxxeFeDsNHv2SrZeWoA+6ZiTyWYWtScEIW07EAcA==
  dependencies:
    "@babel/compat-data" "^7.26.8"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz"
  integrity sha512-vSGCvMecvFCd/BdpGlhpXYNhhC4ccxyvQWpbGL4CWbvfEoLFWUZuSuf7s9Aw70flgQF+6vptvgK2IfOnKlRmBg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.27.0"
    semver "^6.3.1"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz"
  integrity sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz"
  integrity sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.26.5":
  version "7.26.5"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz"
  integrity sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==

"@babel/helper-replace-supers@^7.26.5":
  version "7.26.5"
  resolved "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz"
  integrity sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.26.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz"
  integrity sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helpers@^7.26.10":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.0.tgz"
  integrity sha512-U5eyP/CTFPuNE3qk+WZMxFkp/4zUzdceQlfzf7DdGdhp+Fezd7HD+i8Y24ZuTMKX3wQBld449jijbGq6OdGNQg==
  dependencies:
    "@babel/template" "^7.27.0"
    "@babel/types" "^7.27.0"

"@babel/parser@^7.22.6", "@babel/parser@^7.26.10", "@babel/parser@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz"
  integrity sha512-iaepho73/2Pz7w2eMS0Q5f83+0RKI7i4xmiYeBmDzfRVbQtTOG7Ts0S4HzJVsTMGI9keU8rNfuZr8DKfSt7Yyg==
  dependencies:
    "@babel/types" "^7.27.0"

"@babel/plugin-syntax-typescript@^7.25.9":
  version "7.25.9"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz"
  integrity sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typescript@^7.22.5":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.0.tgz"
  integrity sha512-fRGGjO2UEGPjvEcyAZXRXAS8AfdaQoq7HnxAbJoAoW10B9xOKesmmndJv+Sym2a+9FHWZ9KbyyLCe9s0Sn5jtg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.27.0"
    "@babel/helper-plugin-utils" "^7.26.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-syntax-typescript" "^7.25.9"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.20.13", "@babel/runtime@^7.23.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.0.tgz"
  integrity sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.26.9", "@babel/template@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz"
  integrity sha512-2ncevenBqXI6qRMukPlXwHKHchC7RyMuu4xv5JBXRfOGVcTy1mXCD12qrp7Jsoxll1EV3+9sE4GugBVRjT2jFA==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/parser" "^7.27.0"
    "@babel/types" "^7.27.0"

"@babel/traverse@^7.25.9", "@babel/traverse@^7.26.10", "@babel/traverse@^7.26.5", "@babel/traverse@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz"
  integrity sha512-19lYZFzYVQkkHkl4Cy4WrAVcqBkgvV2YM2TU3xG6DIwO7O3ecbDPfW3yM3bjAGcqcQHi+CCtjMR3dIEHxsd6bA==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.27.0"
    "@babel/parser" "^7.27.0"
    "@babel/template" "^7.27.0"
    "@babel/types" "^7.27.0"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.10", "@babel/types@^7.27.0":
  version "7.27.0"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz"
  integrity sha512-H45s8fVLYjbhFH62dIJ3WtmJ6RSPt/3DRO0ZcT2SUiYiQyz3BLVb9ADEnLl91m74aQPS3AzzeajZHYOalWe3bg==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bundled-es-modules/cookie@^2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@bundled-es-modules/cookie/-/cookie-2.0.1.tgz"
  integrity sha512-8o+5fRPLNbjbdGRRmJj3h6Hh1AQJf2dk3qQ/5ZFb+PXkRNiSoMGGUKlsgLfrxneb72axVJyIYji64E2+nNfYyw==
  dependencies:
    cookie "^0.7.2"

"@bundled-es-modules/statuses@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@bundled-es-modules/statuses/-/statuses-1.0.1.tgz"
  integrity sha512-yn7BklA5acgcBr+7w064fGV+SGIFySjCKpqjcWgBAIfrAkY+4GQTJJHQMeT3V/sgz23VTEVV8TtOmkvJAhFVfg==
  dependencies:
    statuses "^2.0.1"

"@bundled-es-modules/tough-cookie@^0.1.6":
  version "0.1.6"
  resolved "https://registry.npmjs.org/@bundled-es-modules/tough-cookie/-/tough-cookie-0.1.6.tgz"
  integrity sha512-dvMHbL464C0zI+Yqxbz6kZ5TOEp7GLW+pry/RWndAR8MJQAXZ2rPmIs8tziTZjeIyhSNZgZbCePtfSbdWqStJw==
  dependencies:
    "@types/tough-cookie" "^4.0.5"
    tough-cookie "^4.1.4"

"@dnd-kit/accessibility@^3.1.1":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@dnd-kit/accessibility/-/accessibility-3.1.1.tgz"
  integrity sha512-2P+YgaXF+gRsIihwwY1gCsQSYnu9Zyj2py8kY5fFvUM1qm2WA2u639R6YNVfU4GWr+ZM5mqEsfHZZLoRONbemw==
  dependencies:
    tslib "^2.0.0"

"@dnd-kit/core@^6.3.1":
  version "6.3.1"
  resolved "https://registry.npmjs.org/@dnd-kit/core/-/core-6.3.1.tgz"
  integrity sha512-xkGBRQQab4RLwgXxoqETICr6S5JlogafbhNsidmrkVv2YRs5MLwpjoF2qpiGjQt8S9AoxtIV603s0GIUpY5eYQ==
  dependencies:
    "@dnd-kit/accessibility" "^3.1.1"
    "@dnd-kit/utilities" "^3.2.2"
    tslib "^2.0.0"

"@dnd-kit/modifiers@^9.0.0":
  version "9.0.0"
  resolved "https://registry.npmjs.org/@dnd-kit/modifiers/-/modifiers-9.0.0.tgz"
  integrity sha512-ybiLc66qRGuZoC20wdSSG6pDXFikui/dCNGthxv4Ndy8ylErY0N3KVxY2bgo7AWwIbxDmXDg3ylAFmnrjcbVvw==
  dependencies:
    "@dnd-kit/utilities" "^3.2.2"
    tslib "^2.0.0"

"@dnd-kit/sortable@^10.0.0":
  version "10.0.0"
  resolved "https://registry.npmjs.org/@dnd-kit/sortable/-/sortable-10.0.0.tgz"
  integrity sha512-+xqhmIIzvAYMGfBYYnbKuNicfSsk4RksY2XdmJhT+HAC01nix6fHCztU68jooFiMUB01Ky3F0FyOvhG/BZrWkg==
  dependencies:
    "@dnd-kit/utilities" "^3.2.2"
    tslib "^2.0.0"

"@dnd-kit/utilities@^3.2.2":
  version "3.2.2"
  resolved "https://registry.npmjs.org/@dnd-kit/utilities/-/utilities-3.2.2.tgz"
  integrity sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==
  dependencies:
    tslib "^2.0.0"

"@excalidraw/excalidraw@^0.17.6":
  version "0.17.6"
  resolved "https://registry.npmjs.org/@excalidraw/excalidraw/-/excalidraw-0.17.6.tgz"
  integrity sha512-fyCl+zG/Z5yhHDh5Fq2ZGmphcrALmuOdtITm8gN4d8w4ntnaopTXcTfnAAaU3VleDC6LhTkoLOTG6P5kgREiIg==

"@ffmpeg/core@^0.12.10":
  version "0.12.10"
  resolved "https://registry.npmjs.org/@ffmpeg/core/-/core-0.12.10.tgz"
  integrity sha512-dzNplnn2Nxle2c2i2rrDhqcB19q9cglCkWnoMTDN9Q9l3PvdjZWd1HfSPjCNWc/p8Q3CT+Es9fWOR0UhAeYQZA==

"@ffmpeg/ffmpeg@^0.12.15":
  version "0.12.15"
  resolved "https://registry.npmjs.org/@ffmpeg/ffmpeg/-/ffmpeg-0.12.15.tgz"
  integrity sha512-1C8Obr4GsN3xw+/1Ww6PFM84wSQAGsdoTuTWPOj2OizsRDLT4CXTaVjPhkw6ARyDus1B9X/L2LiXHqYYsGnRFw==
  dependencies:
    "@ffmpeg/types" "^0.12.4"

"@ffmpeg/types@^0.12.4":
  version "0.12.4"
  resolved "https://registry.npmjs.org/@ffmpeg/types/-/types-0.12.4.tgz"
  integrity sha512-k9vJQNBGTxE5AhYDtOYR5rO5fKsspbg51gbcwtbkw2lCdoIILzklulcjJfIDwrtn7XhDeF2M+THwJ2FGrLeV6A==

"@floating-ui/core@^1.6.0":
  version "1.6.9"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.6.9.tgz"
  integrity sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  version "1.6.13"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.13.tgz"
  integrity sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.0.0":
  version "2.1.2"
  resolved "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz"
  integrity sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz"
  integrity sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==

"@google-cloud/common@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmjs.org/@google-cloud/common/-/common-6.0.0.tgz"
  integrity sha512-IXh04DlkLMxWgYLIUYuHHKXKOUwPDzDgke1ykkkJPe48cGIS9kkL2U/o0pm4ankHLlvzLF/ma1eO86n/bkumIA==
  dependencies:
    "@google-cloud/projectify" "^4.0.0"
    "@google-cloud/promisify" "^4.0.0"
    arrify "^2.0.0"
    duplexify "^4.1.3"
    extend "^3.0.2"
    google-auth-library "^10.0.0-rc.1"
    html-entities "^2.5.2"
    retry-request "^8.0.0"
    teeny-request "^10.0.0"

"@google-cloud/projectify@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@google-cloud/projectify/-/projectify-4.0.0.tgz"
  integrity sha512-MmaX6HeSvyPbWGwFq7mXdo0uQZLGBYCwziiLIGq5JVX+/bdI3SAq6bP98trV5eTWfLuvsMcIC1YJOF2vfteLFA==

"@google-cloud/promisify@^4.0.0":
  version "4.1.0"
  resolved "https://registry.npmjs.org/@google-cloud/promisify/-/promisify-4.1.0.tgz"
  integrity sha512-G/FQx5cE/+DqBbOpA5jKsegGwdPniU6PuIEMt+qxWgFxvxuFOzVmp6zYchtYuwAWV5/8Dgs0yAmjvNZv3uXLQg==

"@google-cloud/speech@^7.0.1":
  version "7.0.1"
  resolved "https://registry.npmjs.org/@google-cloud/speech/-/speech-7.0.1.tgz"
  integrity sha512-GWL4cjyEAK6w1LD+e02wW9RB8hb4MPYXxjxEp3R4acFkNq1k/mBjIQwQP+147Omq2q6mZUBk/OGuOo/jCEL/Bg==
  dependencies:
    "@google-cloud/common" "^6.0.0"
    "@types/pumpify" "^1.4.4"
    google-gax "^5.0.1-rc.0"
    pumpify "^2.0.1"
    stream-events "^1.0.5"
    uuid "^11.1.0"

"@google/generative-ai@^0.24.0":
  version "0.24.0"
  resolved "https://registry.npmjs.org/@google/generative-ai/-/generative-ai-0.24.0.tgz"
  integrity sha512-fnEITCGEB7NdX0BhoYZ/cq/7WPZ1QS5IzJJfC3Tg/OwkvBetMiVJciyaan297OvE4B9Jg1xvo0zIazX/9sGu1Q==

"@grpc/grpc-js@^1.12.6":
  version "1.13.3"
  resolved "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.13.3.tgz"
  integrity sha512-FTXHdOoPbZrBjlVLHuKbDZnsTxXv2BlHF57xw6LuThXacXvtkahEPED0CKMk6obZDf65Hv4k3z62eyPNpvinIg==
  dependencies:
    "@grpc/proto-loader" "^0.7.13"
    "@js-sdsl/ordered-map" "^4.4.2"

"@grpc/proto-loader@^0.7.13":
  version "0.7.13"
  resolved "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.13.tgz"
  integrity sha512-AiXO/bfe9bmxBjxxtYxFAXGZvMaN5s8kO+jBHAJCON8rJoB5YS/D6X7ZNc6XQkuHNmyl4CYaMI1fJ/Gn27RGGw==
  dependencies:
    lodash.camelcase "^4.3.0"
    long "^5.0.0"
    protobufjs "^7.2.5"
    yargs "^17.7.2"

"@hocuspocus/common@^2.15.2":
  version "2.15.2"
  resolved "https://registry.npmjs.org/@hocuspocus/common/-/common-2.15.2.tgz"
  integrity sha512-wU1wxXNnQQMXyeL3mdSDYiQsm/r/QyJVjjQhF7sUBrLnjdsN7bA1cvfcSvJBr1ymrMSeYRmUL3UlQmEHEOaP7w==
  dependencies:
    lib0 "^0.2.87"

"@hocuspocus/provider@^2.13.6":
  version "2.15.2"
  resolved "https://registry.npmjs.org/@hocuspocus/provider/-/provider-2.15.2.tgz"
  integrity sha512-mdBurviyaUd7bQx4vMIE39WqRJDTpfFelHOVXr7w/jA8G1E7K7lxQ9/DacSrbg+9o8s+1z1+SerZiUjaToaBJg==
  dependencies:
    "@hocuspocus/common" "^2.15.2"
    "@lifeomic/attempt" "^3.0.2"
    lib0 "^0.2.87"
    ws "^8.17.1"

"@hookform/resolvers@^3.9.1":
  version "3.10.0"
  resolved "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.10.0.tgz"
  integrity sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==

"@inquirer/confirm@^5.0.0":
  version "5.1.9"
  resolved "https://registry.npmjs.org/@inquirer/confirm/-/confirm-5.1.9.tgz"
  integrity sha512-NgQCnHqFTjF7Ys2fsqK2WtnA8X1kHyInyG+nMIuHowVTIgIuS10T4AznI/PvbqSpJqjCUqNBlKGh1v3bwLFL4w==
  dependencies:
    "@inquirer/core" "^10.1.10"
    "@inquirer/type" "^3.0.6"

"@inquirer/core@^10.1.10":
  version "10.1.10"
  resolved "https://registry.npmjs.org/@inquirer/core/-/core-10.1.10.tgz"
  integrity sha512-roDaKeY1PYY0aCqhRmXihrHjoSW2A00pV3Ke5fTpMCkzcGF64R8e0lw3dK+eLEHwS4vB5RnW1wuQmvzoRul8Mw==
  dependencies:
    "@inquirer/figures" "^1.0.11"
    "@inquirer/type" "^3.0.6"
    ansi-escapes "^4.3.2"
    cli-width "^4.1.0"
    mute-stream "^2.0.0"
    signal-exit "^4.1.0"
    wrap-ansi "^6.2.0"
    yoctocolors-cjs "^2.1.2"

"@inquirer/figures@^1.0.11":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@inquirer/figures/-/figures-1.0.11.tgz"
  integrity sha512-eOg92lvrn/aRUqbxRyvpEWnrvRuTYRifixHkYVpJiygTgVSBIHDqLh0SrMQXkafvULg3ck11V7xvR+zcgvpHFw==

"@inquirer/type@^3.0.6":
  version "3.0.6"
  resolved "https://registry.npmjs.org/@inquirer/type/-/type-3.0.6.tgz"
  integrity sha512-/mKVCtVpyBu3IDarv0G+59KC4stsD5mDsGpYh+GKs1NZT88Jh52+cuoA1AtLk2Q0r/quNl+1cSUyLRHBFeD0XA==

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@isaacs/fs-minipass@^4.0.0":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz"
  integrity sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==
  dependencies:
    minipass "^7.0.4"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  integrity sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@js-sdsl/ordered-map@^4.4.2":
  version "4.4.2"
  resolved "https://registry.npmjs.org/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz"
  integrity sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==

"@lifeomic/attempt@^3.0.2":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@lifeomic/attempt/-/attempt-3.1.0.tgz"
  integrity sha512-QZqem4QuAnAyzfz+Gj5/+SLxqwCAw2qmt7732ZXodr6VDWGeYLG6w1i/vYLa55JQM9wRuBKLmXmiZ2P0LtE5rw==

"@mswjs/interceptors@^0.37.0":
  version "0.37.6"
  resolved "https://registry.npmjs.org/@mswjs/interceptors/-/interceptors-0.37.6.tgz"
  integrity sha512-wK+5pLK5XFmgtH3aQ2YVvA3HohS3xqV/OxuVOdNx9Wpnz7VE/fnC+e1A7ln6LFYeck7gOJ/dsZV6OLplOtAJ2w==
  dependencies:
    "@open-draft/deferred-promise" "^2.2.0"
    "@open-draft/logger" "^0.3.0"
    "@open-draft/until" "^2.0.0"
    is-node-process "^1.2.0"
    outvariant "^1.4.3"
    strict-event-emitter "^0.5.1"

"@next/env@14.2.16":
  version "14.2.16"
  resolved "https://registry.npmjs.org/@next/env/-/env-14.2.16.tgz"
  integrity sha512-fLrX5TfJzHCbnZ9YUSnGW63tMV3L4nSfhgOQ0iCcX21Pt+VSTDuaLsSuL8J/2XAiVA5AnzvXDpf6pMs60QxOag==

"@next/swc-darwin-arm64@14.2.16":
  version "14.2.16"
  resolved "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-14.2.16.tgz"
  integrity sha512-uFT34QojYkf0+nn6MEZ4gIWQ5aqGF11uIZ1HSxG+cSbj+Mg3+tYm8qXYd3dKN5jqKUm5rBVvf1PBRO/MeQ6rxw==

"@next/swc-darwin-x64@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-darwin-x64/-/swc-darwin-x64-14.2.16.tgz#c6307af69699583ef39b41b182bed76a3c2c9461"
  integrity sha512-mCecsFkYezem0QiZlg2bau3Xul77VxUD38b/auAjohMA22G9KTJneUYMv78vWoCCFkleFAhY1NIvbyjj1ncG9g==

"@next/swc-linux-arm64-gnu@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-14.2.16.tgz#47a74cb824cb185840f6fbea90dec9fc7a248a33"
  integrity sha512-yhkNA36+ECTC91KSyZcgWgKrYIyDnXZj8PqtJ+c2pMvj45xf7y/HrgI17hLdrcYamLfVt7pBaJUMxADtPaczHA==

"@next/swc-linux-arm64-musl@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-14.2.16.tgz#bbbdaab8aa939d12fd3b3b9ad84f6f3964cafeb4"
  integrity sha512-X2YSyu5RMys8R2lA0yLMCOCtqFOoLxrq2YbazFvcPOE4i/isubYjkh+JCpRmqYfEuCVltvlo+oGfj/b5T2pKUA==

"@next/swc-linux-x64-gnu@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-14.2.16.tgz#df9f542c9391f8ce32979ee32cff4773f92cd712"
  integrity sha512-9AGcX7VAkGbc5zTSa+bjQ757tkjr6C/pKS7OK8cX7QEiK6MHIIezBLcQ7gQqbDW2k5yaqba2aDtaBeyyZh1i6Q==

"@next/swc-linux-x64-musl@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-14.2.16.tgz#4c7792fbd67561d06228ec6a4de73faf22f40d47"
  integrity sha512-Klgeagrdun4WWDaOizdbtIIm8khUDQJ/5cRzdpXHfkbY91LxBXeejL4kbZBrpR/nmgRrQvmz4l3OtttNVkz2Sg==

"@next/swc-win32-arm64-msvc@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-14.2.16.tgz#d556ba513ec78452239e295d0b9096ba0053e631"
  integrity sha512-PwW8A1UC1Y0xIm83G3yFGPiOBftJK4zukTmk7DI1CebyMOoaVpd8aSy7K6GhobzhkjYvqS/QmzcfsWG2Dwizdg==

"@next/swc-win32-ia32-msvc@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-win32-ia32-msvc/-/swc-win32-ia32-msvc-14.2.16.tgz#6d093a33bc285404b1cd817959ce6129f4b32c02"
  integrity sha512-jhPl3nN0oKEshJBNDAo0etGMzv0j3q3VYorTSFqH1o3rwv1MQRdor27u1zhkgsHPNeY1jxcgyx1ZsCkDD1IHgg==

"@next/swc-win32-x64-msvc@14.2.16":
  version "14.2.16"
  resolved "https://registry.yarnpkg.com/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-14.2.16.tgz#4d8e89f47a2ea53b040cc9fee0a351b0bb6188c4"
  integrity sha512-OA7NtfxgirCjfqt+02BqxC3MIgM/JaGjw9tOe4fyZgPsqfseNiMPnCRP44Pfs+Gpo9zPN+SXaFsgP6vk8d571A==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@open-draft/deferred-promise@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmjs.org/@open-draft/deferred-promise/-/deferred-promise-2.2.0.tgz"
  integrity sha512-CecwLWx3rhxVQF6V4bAgPS5t+So2sTbPgAzafKkVizyi7tlwpcFpdFqq+wqF2OwNBmqFuu6tOyouTuxgpMfzmA==

"@open-draft/logger@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@open-draft/logger/-/logger-0.3.0.tgz"
  integrity sha512-X2g45fzhxH238HKO4xbSr7+wBS8Fvw6ixhTDuvLd5mqh6bJJCFAPwU9mPDxbcrRtfxv4u5IHCEH77BmxvXmmxQ==
  dependencies:
    is-node-process "^1.2.0"
    outvariant "^1.4.0"

"@open-draft/until@^2.0.0", "@open-draft/until@^2.1.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@open-draft/until/-/until-2.1.0.tgz"
  integrity sha512-U69T3ItWHvLwGg5eJ0n3I62nWuE6ilHlmz7zM0npLBRvPRd7e6NYmg54vvRtP5mZG7kZqZCFVdsTWo7BPtBujg==

"@opentelemetry/api@1.9.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz"
  integrity sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@^2.9.0":
  version "2.11.8"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==

"@radix-ui/number@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz"
  integrity sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==

"@radix-ui/primitive@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz"
  integrity sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==

"@radix-ui/react-accordion@^1.2.1":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4.tgz"
  integrity sha512-SGCxlSBaMvEzDROzyZjsVNzu9XY5E28B3k8jOENyrz6csOv/pG1eHyYfLJai1n9tRjwG61coXDhfpgtxKxUv5g==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collapsible" "1.1.4"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-alert-dialog@^1.1.2":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7.tgz"
  integrity sha512-7Gx1gcoltd0VxKoR8mc+TAVbzvChJyZryZsTam0UhoL92z0L+W8ovxvcgvd+nkz24y7Qc51JQKBAGe4+825tYw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.7"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-arrow@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3.tgz"
  integrity sha512-2dvVU4jva0qkNZH6HHWuSz5FN5GeU5tymvCgutF8WaXz9WnD1NgUhy73cqzkjkN4Zkn8lfTPv5JIfrC221W+Nw==
  dependencies:
    "@radix-ui/react-primitive" "2.0.3"

"@radix-ui/react-avatar@^1.1.1":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4.tgz"
  integrity sha512-+kBesLBzwqyDiYCtYFK+6Ktf+N7+Y6QOTUueLGLIbLZ/YeyFW6bsBGDsN+5HxHpM55C90u5fxsg0ErxzXTcwKA==
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-checkbox@^1.1.1":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5.tgz"
  integrity sha512-B0gYIVxl77KYDR25AY9EGe/G//ef85RVBIxQvK+m5pxAC7XihAc/8leMHhDvjvhDu02SBSb6BuytlWr/G7F3+g==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4.tgz"
  integrity sha512-u7LCw1EYInQtBNLGjm9nZ89S/4GcvX1UR5XbekEgnQae2Hkpq39ycJ1OhdeN1/JDfVNG91kWaWoest127TaEKQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3.tgz"
  integrity sha512-mM2pxoQw5HJ49rkzwOs7Y6J4oYH22wS8BfK2/bBxROlI4xuR0c4jEenQP63LlTlDkO6Buj2Vt+QYAYcOgqtrXA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-compose-refs@1.1.2", "@radix-ui/react-compose-refs@^1.1.1":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz"
  integrity sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==

"@radix-ui/react-context@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz"
  integrity sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==

"@radix-ui/react-dialog@1.1.7", "@radix-ui/react-dialog@^1.1.1", "@radix-ui/react-dialog@^1.1.6":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.7.tgz"
  integrity sha512-EIdma8C0C/I6kL6sO02avaCRqi3fmWJpxH6mqbVScorW6nNktzKJT/le7VPho3o/7wCsyRg3z0+Q+Obr0Gy/VQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.3"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz"
  integrity sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==

"@radix-ui/react-dismissable-layer@1.1.6":
  version "1.1.6"
  resolved "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.6.tgz"
  integrity sha512-7gpgMT2gyKym9Jz2ZhlRXSg2y6cNQIK8d/cqBZ0RBCaps8pFryCWXiUKI+uHGFrhMrbGUP7U6PWgiXzIxoyF3Q==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.1":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.7.tgz"
  integrity sha512-7/1LiuNZuCQE3IzdicGoHdQOHkS2Q08+7p8w6TXZ6ZjgAULaCI85ZY15yPl4o4FVgoKLRT43/rsfNVN8osClQQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.7"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-focus-guards@1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz"
  integrity sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==

"@radix-ui/react-focus-scope@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.3.tgz"
  integrity sha512-4XaDlq0bPt7oJwR+0k0clCiCO/7lO7NKZTAaJBYxDNQT/vj4ig0/UvctrRscZaFREpRvUTkpKR96ov1e6jptQg==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-hover-card@^1.1.7":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-hover-card/-/react-hover-card-1.1.7.tgz"
  integrity sha512-HwM03kP8psrv21J1+9T/hhxi0f5rARVbqIZl9+IAq13l4j4fX+oGIuxisukZZmebO7J35w9gpoILvtG8bbph0w==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-popper" "1.2.3"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-icons@^1.3.0":
  version "1.3.2"
  resolved "https://registry.npmjs.org/@radix-ui/react-icons/-/react-icons-1.3.2.tgz"
  integrity sha512-fyQIhGDhzfc9pK2kH6Pl9c4BDJGfMkPqkyIgYDthyNYoNg3wVhoJMMh19WS4Up/1KMPFVpNsT2q3WmXn2N1m6g==

"@radix-ui/react-id@1.1.1", "@radix-ui/react-id@^1.1.0":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz"
  integrity sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.1":
  version "2.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3.tgz"
  integrity sha512-zwSQ1NzSKG95yA0tvBMgv6XPHoqapJCcg9nsUBaQQ66iRBhZNhlpaQG2ERYYX4O4stkYFK5rxj5NsWfO9CS+Hg==
  dependencies:
    "@radix-ui/react-primitive" "2.0.3"

"@radix-ui/react-menu@2.1.7":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.7.tgz"
  integrity sha512-tBODsrk68rOi1/iQzbM54toFF+gSw/y+eQgttFflqlGekuSebNqvFNHjJgjqPhiMb4Fw9A0zNFly1QT6ZFdQ+Q==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.3"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.3"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-roving-focus" "1.1.3"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popover@^1.1.2":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.7.tgz"
  integrity sha512-I38OYWDmJF2kbO74LX8UsFydSHWOJuQ7LxPnTefjxxvdvPLempvAnmsyX9UsBlywcbSGpRH7oMLfkUf+ij4nrw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.3"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.3"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.3":
  version "1.2.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3.tgz"
  integrity sha512-iNb9LYUMkne9zIahukgQmHlSBp9XWGeQQ7FvUGNk45ywzOb6kQa+Ca38OphXlWDiKvyneo9S+KSJsLfLt8812A==
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.5":
  version "1.1.5"
  resolved "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5.tgz"
  integrity sha512-ps/67ZqsFm+Mb6lSPJpfhRLrVL2i2fntgCmGMqqth4eaGUf+knAuuRtWVJrNjUhExgmdRqftSgzpf0DF0n6yXA==
  dependencies:
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3.tgz"
  integrity sha512-IrVLIhskYhH3nLvtcBLQFZr61tBG7wx7O3kEmdzcYwRGAEBmBicGGL7ATzNgruYJ3xBTbuzEEq9OXJM3PAX3tA==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@2.0.3", "@radix-ui/react-primitive@^2.0.2":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3.tgz"
  integrity sha512-Pf/t/GkndH7CQ8wE2hbkXA+WyZ83fhQQn5DDmwDiDo6AwN/fhaH8oqZ0jRjMrO2iaMhDi6P1HRx6AZwyMinY1g==
  dependencies:
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-progress@^1.1.1":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.3.tgz"
  integrity sha512-F56aZPGTPb4qJQ/vDjnAq63oTu/DRoIG/Asb5XKOWj8rpefNLtUllR969j5QDN2sRrTk9VXIqQDRj5VvAuquaw==
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"

"@radix-ui/react-radio-group@^1.2.1":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.2.4.tgz"
  integrity sha512-oLz7ATfKgVTUbpr5OBu6Q7hQcnV22uPT306bmG0QwgnKqBStR98RfWfJGCfW/MmhL4ISmrmmBPBW+c77SDwV9g==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-roving-focus" "1.1.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.3.tgz"
  integrity sha512-ufbpLUjZiOg4iYgb2hQrWXEPYX6jOLBbR27bDyAff5GYMRrCzcze8lukjuXVUQvJ6HZe8+oL+hhswDcjmcgVyg==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-scroll-area@^1.2.3":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4.tgz"
  integrity sha512-G9rdWTQjOR4sk76HwSdROhPU0jZWpfozn9skU1v4N0/g9k7TmswrJn8W8WMU+aYktnLLpk5LX6fofj2bGe5NFQ==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.1.1":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.7.tgz"
  integrity sha512-exzGIRtc7S8EIM2KjFg+7lJZsH7O7tpaBaJbBNVDnOZNhtoQ2iV+iSNfi2Wth0m6h3trJkMVvzAehB3c6xj/3Q==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.3"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.3"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.1.3"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.1.2":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.3.tgz"
  integrity sha512-2omrWKJvxR0U/tkIXezcc1nFMwtLU0+b/rDK40gnzJqTLWQ/TD/D5IYVefp9sC3QWfeQbpSbEA6op9MQKyaALQ==
  dependencies:
    "@radix-ui/react-primitive" "2.0.3"

"@radix-ui/react-slider@^1.2.1":
  version "1.2.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4.tgz"
  integrity sha512-Vr/OgNejNJPAghIhjS7Mf/2F/EXGDT0qgtiHf2BHz71+KqgN+jndFLKq5xAB9JOGejGzejfJLIvT04Do+yzhcg==
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@1.2.0", "@radix-ui/react-slot@^1.1.1":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz"
  integrity sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.1.0":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4.tgz"
  integrity sha512-zGP6W8plLeogoeGMiTHJ/uvf+TE1C2chVsEwfP8YlvpQKJHktG+iCkUtCLGPAuDV8/qDSmIRPm4NggaTxFMVBQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.1":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4.tgz"
  integrity sha512-fuHMHWSf5SRhXke+DbHXj2wVMo+ghVH30vhX3XVacdXqDl+J4XWafMIGOOER861QpBx1jxgwKXL2dQnfrsd8MQ==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-roving-focus" "1.1.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-toast@^1.2.2":
  version "1.2.7"
  resolved "https://registry.npmjs.org/@radix-ui/react-toast/-/react-toast-1.2.7.tgz"
  integrity sha512-0IWTbAUKvzdpOaWDMZisXZvScXzF0phaQjWspK8RUMEUxjLbli+886mB/kXTIC3F+t5vQ0n0vYn+dsX8s+WdfA==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.1.3"

"@radix-ui/react-toggle@^1.1.0":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3.tgz"
  integrity sha512-Za5HHd9nvsiZ2t3EI/dVd4Bm/JydK+D22uHKk46fPtvuPxVCJBUo5mQybN+g5sZe35y7I6GDTTfdkVv5SnxlFg==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-use-controllable-state" "1.1.1"

"@radix-ui/react-tooltip@^1.1.2":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.2.0.tgz"
  integrity sha512-b1Sdc75s7zN9B8ONQTGBSHL3XS8+IcjcOIY51fhM4R1Hx8s0YbgqgyNZiri4qcYMVZK8hfCZVBiyCm7N9rs0rw==
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.6"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.3"
    "@radix-ui/react-portal" "1.1.5"
    "@radix-ui/react-presence" "1.1.3"
    "@radix-ui/react-primitive" "2.0.3"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.1.3"

"@radix-ui/react-use-callback-ref@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz"
  integrity sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==

"@radix-ui/react-use-controllable-state@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.1.tgz"
  integrity sha512-YnEXIy8/ga01Y1PN0VfaNH//MhA91JlEGVBDxDzROqwrAtG5Yr2QGEPz8A/rJA3C7ZAHryOYGaUv8fLSW2H/mg==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz"
  integrity sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-layout-effect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz"
  integrity sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==

"@radix-ui/react-use-previous@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz"
  integrity sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==

"@radix-ui/react-use-rect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz"
  integrity sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz"
  integrity sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.1.3":
  version "1.1.3"
  resolved "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.3.tgz"
  integrity sha512-oXSF3ZQRd5fvomd9hmUCb2EHSZbPp3ZSHAHJJU/DlF9XoFkJBBW8RHU/E8WEH+RbSfJd/QFA0sl8ClJXknBwHQ==
  dependencies:
    "@radix-ui/react-primitive" "2.0.3"

"@radix-ui/rect@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.1.tgz"
  integrity sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==

"@react-dnd/asap@^5.0.1":
  version "5.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/asap/-/asap-5.0.2.tgz"
  integrity sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==

"@react-dnd/invariant@^4.0.1":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-4.0.2.tgz"
  integrity sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==

"@react-dnd/shallowequal@^4.0.1":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@react-dnd/shallowequal/-/shallowequal-4.0.2.tgz"
  integrity sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==

"@react-email/render@1.0.6":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@react-email/render/-/render-1.0.6.tgz"
  integrity sha512-zNueW5Wn/4jNC1c5LFgXzbUdv5Lhms+FWjOvWAhal7gx5YVf0q6dPJ0dnR70+ifo59gcMLwCZEaTS9EEuUhKvQ==
  dependencies:
    html-to-text "9.0.5"
    prettier "3.5.3"
    react-promise-suspense "0.3.4"

"@react-pdf/fns@3.1.2":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@react-pdf/fns/-/fns-3.1.2.tgz"
  integrity sha512-qTKGUf0iAMGg2+OsUcp9ffKnKi41RukM/zYIWMDJ4hRVYSr89Q7e3wSDW/Koqx3ea3Uy/z3h2y3wPX6Bdfxk6g==

"@react-pdf/font@^4.0.2":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@react-pdf/font/-/font-4.0.2.tgz"
  integrity sha512-/dAWu7Y2RD1RxarDZ9SkYPHgBYOhmcDnet4W/qN/m8k+A2Hr3ja54GymSR7GGxWBtxjKtNauVKrTa9LS1n8WUw==
  dependencies:
    "@react-pdf/pdfkit" "^4.0.3"
    "@react-pdf/types" "^2.9.0"
    fontkit "^2.0.2"
    is-url "^1.2.4"

"@react-pdf/image@^3.0.3":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@react-pdf/image/-/image-3.0.3.tgz"
  integrity sha512-lvP5ryzYM3wpbO9bvqLZYwEr5XBDX9jcaRICvtnoRqdJOo7PRrMnmB4MMScyb+Xw10mGeIubZAAomNAG5ONQZQ==
  dependencies:
    "@react-pdf/png-js" "^3.0.0"
    jay-peg "^1.1.1"

"@react-pdf/layout@^4.4.0":
  version "4.4.0"
  resolved "https://registry.npmjs.org/@react-pdf/layout/-/layout-4.4.0.tgz"
  integrity sha512-Aq+Cc6JYausWLoks2FvHe3PwK9cTuvksB2uJ0AnkKJEUtQbvCq8eCRb1bjbbwIji9OzFRTTzZij7LzkpKHjIeA==
  dependencies:
    "@react-pdf/fns" "3.1.2"
    "@react-pdf/image" "^3.0.3"
    "@react-pdf/primitives" "^4.1.1"
    "@react-pdf/stylesheet" "^6.1.0"
    "@react-pdf/textkit" "^6.0.0"
    "@react-pdf/types" "^2.9.0"
    emoji-regex "^10.3.0"
    queue "^6.0.1"
    yoga-layout "^3.2.1"

"@react-pdf/pdfkit@^4.0.3":
  version "4.0.3"
  resolved "https://registry.npmjs.org/@react-pdf/pdfkit/-/pdfkit-4.0.3.tgz"
  integrity sha512-k+Lsuq8vTwWsCqTp+CCB4+2N+sOTFrzwGA7aw3H9ix/PDWR9QksbmNg0YkzGbLAPI6CeawmiLHcf4trZ5ecLPQ==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/png-js" "^3.0.0"
    browserify-zlib "^0.2.0"
    crypto-js "^4.2.0"
    fontkit "^2.0.2"
    jay-peg "^1.1.1"
    linebreak "^1.1.0"
    vite-compatible-readable-stream "^3.6.1"

"@react-pdf/png-js@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@react-pdf/png-js/-/png-js-3.0.0.tgz"
  integrity sha512-eSJnEItZ37WPt6Qv5pncQDxLJRK15eaRwPT+gZoujP548CodenOVp49GST8XJvKMFt9YqIBzGBV/j9AgrOQzVA==
  dependencies:
    browserify-zlib "^0.2.0"

"@react-pdf/primitives@^4.1.1":
  version "4.1.1"
  resolved "https://registry.npmjs.org/@react-pdf/primitives/-/primitives-4.1.1.tgz"
  integrity sha512-IuhxYls1luJb7NUWy6q5avb1XrNaVj9bTNI40U9qGRuS6n7Hje/8H8Qi99Z9UKFV74bBP3DOf3L1wV2qZVgVrQ==

"@react-pdf/reconciler@^1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@react-pdf/reconciler/-/reconciler-1.1.4.tgz"
  integrity sha512-oTQDiR/t4Z/Guxac88IavpU2UgN7eR0RMI9DRKvKnvPz2DUasGjXfChAdMqDNmJJxxV26mMy9xQOUV2UU5/okg==
  dependencies:
    object-assign "^4.1.1"
    scheduler "0.25.0-rc-603e6108-20241029"

"@react-pdf/render@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@react-pdf/render/-/render-4.3.0.tgz"
  integrity sha512-MdWfWaqO6d7SZD75TZ2z5L35V+cHpyA43YNRlJNG0RJ7/MeVGDQv12y/BXOJgonZKkeEGdzM3EpAt9/g4E22WA==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "3.1.2"
    "@react-pdf/primitives" "^4.1.1"
    "@react-pdf/textkit" "^6.0.0"
    "@react-pdf/types" "^2.9.0"
    abs-svg-path "^0.1.1"
    color-string "^1.9.1"
    normalize-svg-path "^1.1.0"
    parse-svg-path "^0.1.2"
    svg-arc-to-cubic-bezier "^3.2.0"

"@react-pdf/renderer@^4.1.5":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@react-pdf/renderer/-/renderer-4.3.0.tgz"
  integrity sha512-28gpA69fU9ZQrDzmd5xMJa1bDf8t0PT3ApUKBl2PUpoE/x4JlvCB5X66nMXrfFrgF2EZrA72zWQAkvbg7TE8zw==
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@react-pdf/fns" "3.1.2"
    "@react-pdf/font" "^4.0.2"
    "@react-pdf/layout" "^4.4.0"
    "@react-pdf/pdfkit" "^4.0.3"
    "@react-pdf/primitives" "^4.1.1"
    "@react-pdf/reconciler" "^1.1.4"
    "@react-pdf/render" "^4.3.0"
    "@react-pdf/types" "^2.9.0"
    events "^3.3.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    queue "^6.0.1"

"@react-pdf/stylesheet@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmjs.org/@react-pdf/stylesheet/-/stylesheet-6.1.0.tgz"
  integrity sha512-BGZ2sYNUp38VJUegjva/jsri3iiRGnVNjWI+G9dTwAvLNOmwFvSJzqaCsEnqQ/DW5mrTBk/577FhDY7pv6AidA==
  dependencies:
    "@react-pdf/fns" "3.1.2"
    "@react-pdf/types" "^2.9.0"
    color-string "^1.9.1"
    hsl-to-hex "^1.0.0"
    media-engine "^1.0.3"
    postcss-value-parser "^4.1.0"

"@react-pdf/textkit@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmjs.org/@react-pdf/textkit/-/textkit-6.0.0.tgz"
  integrity sha512-fDt19KWaJRK/n2AaFoVm31hgGmpygmTV7LsHGJNGZkgzXcFyLsx+XUl63DTDPH3iqxj3xUX128t104GtOz8tTw==
  dependencies:
    "@react-pdf/fns" "3.1.2"
    bidi-js "^1.0.2"
    hyphen "^1.6.4"
    unicode-properties "^1.4.1"

"@react-pdf/types@^2.9.0":
  version "2.9.0"
  resolved "https://registry.npmjs.org/@react-pdf/types/-/types-2.9.0.tgz"
  integrity sha512-ckj80vZLlvl9oYrQ4tovEaqKWP3O06Eb1D48/jQWbdwz1Yh7Y9v1cEmwlP8ET+a1Whp8xfdM0xduMexkuPANCQ==
  dependencies:
    "@react-pdf/font" "^4.0.2"
    "@react-pdf/primitives" "^4.1.1"
    "@react-pdf/stylesheet" "^6.1.0"

"@remirror/core-constants@3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@remirror/core-constants/-/core-constants-3.0.0.tgz"
  integrity sha512-42aWfPrimMfDKDi4YegyS7x+/0tlzaqwPQCULLanv3DMIlu96KTJR0fM5isWX2UViOqlGnX6YFgqWepcX+XMNg==

"@remixicon/react@^4.6.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@remixicon/react/-/react-4.6.0.tgz"
  integrity sha512-bY56maEgT5IYUSRotqy9h03IAKJC85vlKtWFg2FKzfs8JPrkdBAYSa9dxoUSKFwGzup8Ux6vjShs9Aec3jvr2w==

"@selderee/plugin-htmlparser2@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@selderee/plugin-htmlparser2/-/plugin-htmlparser2-0.11.0.tgz"
  integrity sha512-P33hHGdldxGabLFjPPpaTxVolMrzrcegejx+0GxjrIb9Zv48D8yAIA/QTDR2dFl7Uz7urX8aX6+5bCZslr+gWQ==
  dependencies:
    domhandler "^5.0.3"
    selderee "^0.11.0"

"@supabase/auth-helpers-nextjs@^0.10.0":
  version "0.10.0"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-nextjs/-/auth-helpers-nextjs-0.10.0.tgz"
  integrity sha512-2dfOGsM4yZt0oS4TPiE7bD4vf7EVz7NRz/IJrV6vLg0GP7sMUx8wndv2euLGq4BjN9lUCpu6DG/uCC8j+ylwPg==
  dependencies:
    "@supabase/auth-helpers-shared" "0.7.0"
    set-cookie-parser "^2.6.0"

"@supabase/auth-helpers-shared@0.7.0":
  version "0.7.0"
  resolved "https://registry.npmjs.org/@supabase/auth-helpers-shared/-/auth-helpers-shared-0.7.0.tgz"
  integrity sha512-FBFf2ei2R7QC+B/5wWkthMha8Ca2bWHAndN+syfuEUUfufv4mLcAgBCcgNg5nJR8L0gZfyuaxgubtOc9aW3Cpg==
  dependencies:
    jose "^4.14.4"

"@supabase/auth-js@2.69.1":
  version "2.69.1"
  resolved "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.69.1.tgz"
  integrity sha512-FILtt5WjCNzmReeRLq5wRs3iShwmnWgBvxHfqapC/VoljJl+W8hDAyFmf1NVw3zH+ZjZ05AKxiKxVeb0HNWRMQ==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/functions-js@2.4.4":
  version "2.4.4"
  resolved "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz"
  integrity sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/node-fetch@2.6.15", "@supabase/node-fetch@^2.6.14":
  version "2.6.15"
  resolved "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz"
  integrity sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==
  dependencies:
    whatwg-url "^5.0.0"

"@supabase/postgrest-js@1.19.4":
  version "1.19.4"
  resolved "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz"
  integrity sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/realtime-js@2.11.2":
  version "2.11.2"
  resolved "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.2.tgz"
  integrity sha512-u/XeuL2Y0QEhXSoIPZZwR6wMXgB+RQbJzG9VErA3VghVt7uRfSVsjeqd7m5GhX3JR6dM/WRmLbVR8URpDWG4+w==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"
    "@types/phoenix" "^1.5.4"
    "@types/ws" "^8.5.10"
    ws "^8.18.0"

"@supabase/ssr@latest":
  version "0.6.1"
  resolved "https://registry.npmjs.org/@supabase/ssr/-/ssr-0.6.1.tgz"
  integrity sha512-QtQgEMvaDzr77Mk3vZ3jWg2/y+D8tExYF7vcJT+wQ8ysuvOeGGjYbZlvj5bHYsj/SpC0bihcisnwPrM4Gp5G4g==
  dependencies:
    cookie "^1.0.1"

"@supabase/storage-js@2.7.1":
  version "2.7.1"
  resolved "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz"
  integrity sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==
  dependencies:
    "@supabase/node-fetch" "^2.6.14"

"@supabase/supabase-js@latest":
  version "2.49.4"
  resolved "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.49.4.tgz"
  integrity sha512-jUF0uRUmS8BKt37t01qaZ88H9yV1mbGYnqLeuFWLcdV+x1P4fl0yP9DGtaEhFPZcwSom7u16GkLEH9QJZOqOkw==
  dependencies:
    "@supabase/auth-js" "2.69.1"
    "@supabase/functions-js" "2.4.4"
    "@supabase/node-fetch" "2.6.15"
    "@supabase/postgrest-js" "1.19.4"
    "@supabase/realtime-js" "2.11.2"
    "@supabase/storage-js" "2.7.1"

"@svgdotjs/svg.draggable.js@^3.0.4":
  version "3.0.6"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.draggable.js/-/svg.draggable.js-3.0.6.tgz"
  integrity sha512-7iJFm9lL3C40HQcqzEfezK2l+dW2CpoVY3b77KQGqc8GXWa6LhhmX5Ckv7alQfUXBuZbjpICZ+Dvq1czlGx7gA==

"@svgdotjs/svg.filter.js@^3.0.8":
  version "3.0.9"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.filter.js/-/svg.filter.js-3.0.9.tgz"
  integrity sha512-/69XMRCDoam2HgC4ldHIaDgeQf1ViHIsa0Ld4uWgiXtZ+E24DWHe/9Ib6kbNiZ7WRIdlVokUDR1Fg0kjIpkfbw==
  dependencies:
    "@svgdotjs/svg.js" "^3.2.4"

"@svgdotjs/svg.js@^3.2.4":
  version "3.2.4"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.js/-/svg.js-3.2.4.tgz"
  integrity sha512-BjJ/7vWNowlX3Z8O4ywT58DqbNRyYlkk6Yz/D13aB7hGmfQTvGX4Tkgtm/ApYlu9M7lCQi15xUEidqMUmdMYwg==

"@svgdotjs/svg.resize.js@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.resize.js/-/svg.resize.js-2.0.5.tgz"
  integrity sha512-4heRW4B1QrJeENfi7326lUPYBCevj78FJs8kfeDxn5st0IYPIRXoTtOSYvTzFWgaWWXd3YCDE6ao4fmv91RthA==

"@svgdotjs/svg.select.js@^4.0.1":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@svgdotjs/svg.select.js/-/svg.select.js-4.0.2.tgz"
  integrity sha512-5gWdrvoQX3keo03SCmgaBbD+kFftq0F/f2bzCbNnpkkvW6tk4rl4MakORzFuNjvXPWwB4az9GwuvVxQVnjaK2g==

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz"
  integrity sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==

"@swc/helpers@0.5.5":
  version "0.5.5"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.5.tgz"
  integrity sha512-KGYxvIOXcceOAbEk4bi/dVLEK9z8sZ0uBB3Il5b1rhfClSpcX0yfRO0KmTkqR2cnQDymwLB+25ZyMzICg/cm/A==
  dependencies:
    "@swc/counter" "^0.1.3"
    tslib "^2.4.0"

"@swc/helpers@^0.5.12":
  version "0.5.17"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.17.tgz"
  integrity sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==
  dependencies:
    tslib "^2.8.0"

"@tabler/icons-react@^3.19.0":
  version "3.31.0"
  resolved "https://registry.npmjs.org/@tabler/icons-react/-/icons-react-3.31.0.tgz"
  integrity sha512-2rrCM5y/VnaVKnORpDdAua9SEGuJKVqPtWxeQ/vUVsgaUx30LDgBZph7/lterXxDY1IKR6NO//HDhWiifXTi3w==
  dependencies:
    "@tabler/icons" "3.31.0"

"@tabler/icons@3.31.0":
  version "3.31.0"
  resolved "https://registry.npmjs.org/@tabler/icons/-/icons-3.31.0.tgz"
  integrity sha512-dblAdeKY3+GA1U+Q9eziZ0ooVlZMHsE8dqP0RkwvRtEsAULoKOYaCUOcJ4oW1DjWegdxk++UAt2SlQVnmeHv+g==

"@tanstack/react-table@^8.20.5":
  version "8.21.3"
  resolved "https://registry.npmjs.org/@tanstack/react-table/-/react-table-8.21.3.tgz"
  integrity sha512-5nNMTSETP4ykGegmVkhjcS8tTLW6Vl4axfEGQN3v0zdHYbK4UfoqfPChclTrJ4EoK9QynqAu9oUf8VEmrpZ5Ww==
  dependencies:
    "@tanstack/table-core" "8.21.3"

"@tanstack/table-core@8.21.3":
  version "8.21.3"
  resolved "https://registry.npmjs.org/@tanstack/table-core/-/table-core-8.21.3.tgz"
  integrity sha512-ldZXEhOBb8Is7xLs01fR3YEc3DERiz5silj8tnGkFZytt1abEvl/GhUmCE0PMLaMPTa3Jk4HbKmRlHmu+gCftg==

"@tiptap/core@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/core/-/core-2.11.7.tgz"
  integrity sha512-zN+NFFxLsxNEL8Qioc+DL6b8+Tt2bmRbXH22Gk6F6nD30x83eaUSFlSv3wqvgyCq3I1i1NO394So+Agmayx6rQ==

"@tiptap/extension-blockquote@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-blockquote/-/extension-blockquote-2.11.7.tgz"
  integrity sha512-liD8kWowl3CcYCG9JQlVx1eSNc/aHlt6JpVsuWvzq6J8APWX693i3+zFqyK2eCDn0k+vW62muhSBe3u09hA3Zw==

"@tiptap/extension-bold@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-bold/-/extension-bold-2.11.7.tgz"
  integrity sha512-VTR3JlldBixXbjpLTFme/Bxf1xeUgZZY3LTlt5JDlCW3CxO7k05CIa+kEZ8LXpog5annytZDUVtWqxrNjmsuHQ==

"@tiptap/extension-bubble-menu@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-bubble-menu/-/extension-bubble-menu-2.11.7.tgz"
  integrity sha512-0vYqSUSSap3kk3/VT4tFE1/6StX70I3/NKQ4J68ZSFgkgyB3ZVlYv7/dY3AkEukjsEp3yN7m8Gw8ei2eEwyzwg==
  dependencies:
    tippy.js "^6.3.7"

"@tiptap/extension-bullet-list@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-bullet-list/-/extension-bullet-list-2.11.7.tgz"
  integrity sha512-WbPogE2/Q3e3/QYgbT1Sj4KQUfGAJNc5pvb7GrUbvRQsAh7HhtuO8hqdDwH8dEdD/cNUehgt17TO7u8qV6qeBw==

"@tiptap/extension-code-block@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-code-block/-/extension-code-block-2.11.7.tgz"
  integrity sha512-To/y/2H04VWqiANy53aXjV7S6fA86c2759RsH1hTIe57jA1KyE7I5tlAofljOLZK/covkGmPeBddSPHGJbz++Q==

"@tiptap/extension-code@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-code/-/extension-code-2.11.7.tgz"
  integrity sha512-VpPO1Uy/eF4hYOpohS/yMOcE1C07xmMj0/D989D9aS1x95jWwUVrSkwC+PlWMUBx9PbY2NRsg1ZDwVvlNKZ6yQ==

"@tiptap/extension-document@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-document/-/extension-document-2.11.7.tgz"
  integrity sha512-95ouJXPjdAm9+VBRgFo4lhDoMcHovyl/awORDI8gyEn0Rdglt+ZRZYoySFzbVzer9h0cre+QdIwr9AIzFFbfdA==

"@tiptap/extension-dropcursor@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-dropcursor/-/extension-dropcursor-2.11.7.tgz"
  integrity sha512-63mL+nxQILizsr5NbmgDeOjFEWi34BLt7evwL6UUZEVM15K8V1G8pD9Y0kCXrZYpHWz0tqFRXdrhDz0Ppu8oVw==

"@tiptap/extension-floating-menu@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-floating-menu/-/extension-floating-menu-2.11.7.tgz"
  integrity sha512-DG54WoUu2vxHRVzKZiR5I5RMOYj45IlxQMkBAx1wjS0ch41W8DUYEeipvMMjCeKtEI+emz03xYUcOAP9LRmg+w==
  dependencies:
    tippy.js "^6.3.7"

"@tiptap/extension-gapcursor@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-gapcursor/-/extension-gapcursor-2.11.7.tgz"
  integrity sha512-EceesmPG7FyjXZ8EgeJPUov9G1mAf2AwdypxBNH275g6xd5dmU/KvjoFZjmQ0X1ve7mS+wNupVlGxAEUYoveew==

"@tiptap/extension-hard-break@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-hard-break/-/extension-hard-break-2.11.7.tgz"
  integrity sha512-zTkZSA6q+F5sLOdCkiC2+RqJQN0zdsJqvFIOVFL/IDVOnq6PZO5THzwRRLvOSnJJl3edRQCl/hUgS0L5sTInGQ==

"@tiptap/extension-heading@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-heading/-/extension-heading-2.11.7.tgz"
  integrity sha512-8kWh7y4Rd2fwxfWOhFFWncHdkDkMC1Z60yzIZWjIu72+6yQxvo8w3yeb7LI7jER4kffbMmadgcfhCHC/fkObBA==

"@tiptap/extension-history@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-history/-/extension-history-2.11.7.tgz"
  integrity sha512-Cu5x3aS13I040QSRoLdd+w09G4OCVfU+azpUqxufZxeNs9BIJC+0jowPLeOxKDh6D5GGT2A8sQtxc6a/ssbs8g==

"@tiptap/extension-horizontal-rule@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-horizontal-rule/-/extension-horizontal-rule-2.11.7.tgz"
  integrity sha512-uVmQwD2dzZ5xwmvUlciy0ItxOdOfQjH6VLmu80zyJf8Yu7mvwP8JyxoXUX0vd1xHpwAhgQ9/ozjIWYGIw79DPQ==

"@tiptap/extension-italic@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-italic/-/extension-italic-2.11.7.tgz"
  integrity sha512-r985bkQfG0HMpmCU0X0p/Xe7U1qgRm2mxvcp6iPCuts2FqxaCoyfNZ8YnMsgVK1mRhM7+CQ5SEg2NOmQNtHvPw==

"@tiptap/extension-list-item@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-list-item/-/extension-list-item-2.11.7.tgz"
  integrity sha512-6ikh7Y+qAbkSuIHXPIINqfzmWs5uIGrylihdZ9adaIyvrN1KSnWIqrZIk/NcZTg5YFIJlXrnGSRSjb/QM3WUhw==

"@tiptap/extension-list-keymap@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-list-keymap/-/extension-list-keymap-2.11.7.tgz"
  integrity sha512-t1XgD6+NNxEW9KrI7eK/RnXV/zBsHbYwzn4g0V3+NYQS7siaeCHHN0JwGhVPx1RojYeg+zq9NEj+fr4f3G7XAw==

"@tiptap/extension-ordered-list@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-ordered-list/-/extension-ordered-list-2.11.7.tgz"
  integrity sha512-bLGCHDMB0vbJk7uu8bRg8vES3GsvxkX7Cgjgm/6xysHFbK98y0asDtNxkW1VvuRreNGz4tyB6vkcVCfrxl4jKw==

"@tiptap/extension-paragraph@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-paragraph/-/extension-paragraph-2.11.7.tgz"
  integrity sha512-Pl3B4q6DJqTvvAdraqZaNP9Hh0UWEHL5nNdxhaRNuhKaUo7lq8wbDSIxIW3lvV0lyCs0NfyunkUvSm1CXb6d4Q==

"@tiptap/extension-placeholder@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-placeholder/-/extension-placeholder-2.11.7.tgz"
  integrity sha512-/06zXV4HIjYoiaUq1fVJo/RcU8pHbzx21evOpeG/foCfNpMI4xLU/vnxdUi6/SQqpZMY0eFutDqod1InkSOqsg==

"@tiptap/extension-strike@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-strike/-/extension-strike-2.11.7.tgz"
  integrity sha512-D6GYiW9F24bvAY7XMOARNZbC8YGPzdzWdXd8VOOJABhf4ynMi/oW4NNiko+kZ67jn3EGaKoz32VMJzNQgYi1HA==

"@tiptap/extension-text-align@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-text-align/-/extension-text-align-2.11.7.tgz"
  integrity sha512-3M8zd9ROADXazVNpgR6Ejs1evSvBveN36qN4GgV71GqrNlTcjqYgQcXFLQrsd2hnE+aXir8/8bLJ+aaJXDninA==

"@tiptap/extension-text-style@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-text-style/-/extension-text-style-2.11.7.tgz"
  integrity sha512-LHO6DBg/9SkCQFdWlVfw9nolUmw+Cid94WkTY+7IwrpyG2+ZGQxnKpCJCKyeaFNbDoYAtvu0vuTsSXeCkgShcA==

"@tiptap/extension-text@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-text/-/extension-text-2.11.7.tgz"
  integrity sha512-wObCn8qZkIFnXTLvBP+X8KgaEvTap/FJ/i4hBMfHBCKPGDx99KiJU6VIbDXG8d5ZcFZE0tOetK1pP5oI7qgMlQ==

"@tiptap/extension-typography@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-typography/-/extension-typography-2.11.7.tgz"
  integrity sha512-qyYROxuXuMAMw30RXFYjr9mfZv+7avD3BW+fVEIa3lwnUMFNExHj6j2HMgYvrPVByGXlQU/4uHWcB0uiG0Bf1w==

"@tiptap/extension-underline@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/extension-underline/-/extension-underline-2.11.7.tgz"
  integrity sha512-NtoQw6PGijOAtXC6G+0Aq0/Z5wwEjPhNHs8nsjXogfWIgaj/aI4/zfBnA06eI3WT+emMYQTl0fTc4CUPnLVU8g==

"@tiptap/pm@^2.11.5", "@tiptap/pm@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/pm/-/pm-2.11.7.tgz"
  integrity sha512-7gEEfz2Q6bYKXM07vzLUD0vqXFhC5geWRA6LCozTiLdVFDdHWiBrvb2rtkL5T7mfLq03zc1QhH7rI3F6VntOEA==
  dependencies:
    prosemirror-changeset "^2.2.1"
    prosemirror-collab "^1.3.1"
    prosemirror-commands "^1.6.2"
    prosemirror-dropcursor "^1.8.1"
    prosemirror-gapcursor "^1.3.2"
    prosemirror-history "^1.4.1"
    prosemirror-inputrules "^1.4.0"
    prosemirror-keymap "^1.2.2"
    prosemirror-markdown "^1.13.1"
    prosemirror-menu "^1.2.4"
    prosemirror-model "^1.23.0"
    prosemirror-schema-basic "^1.2.3"
    prosemirror-schema-list "^1.4.1"
    prosemirror-state "^1.4.3"
    prosemirror-tables "^1.6.4"
    prosemirror-trailing-node "^3.0.0"
    prosemirror-transform "^1.10.2"
    prosemirror-view "^1.37.0"

"@tiptap/react@^2.11.5":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/react/-/react-2.11.7.tgz"
  integrity sha512-gQZEUkAoPsBptnB4T2gAtiUxswjVGhfsM9vOElQco+b11DYmy110T2Zuhg+2YGvB/CG3RoWJx34808P0FX1ijA==
  dependencies:
    "@tiptap/extension-bubble-menu" "^2.11.7"
    "@tiptap/extension-floating-menu" "^2.11.7"
    "@types/use-sync-external-store" "^0.0.6"
    fast-deep-equal "^3"
    use-sync-external-store "^1"

"@tiptap/starter-kit@^2.11.5":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@tiptap/starter-kit/-/starter-kit-2.11.7.tgz"
  integrity sha512-K+q51KwNU/l0kqRuV5e1824yOLVftj6kGplGQLvJG56P7Rb2dPbM/JeaDbxQhnHT/KDGamG0s0Po0M3pPY163A==
  dependencies:
    "@tiptap/core" "^2.11.7"
    "@tiptap/extension-blockquote" "^2.11.7"
    "@tiptap/extension-bold" "^2.11.7"
    "@tiptap/extension-bullet-list" "^2.11.7"
    "@tiptap/extension-code" "^2.11.7"
    "@tiptap/extension-code-block" "^2.11.7"
    "@tiptap/extension-document" "^2.11.7"
    "@tiptap/extension-dropcursor" "^2.11.7"
    "@tiptap/extension-gapcursor" "^2.11.7"
    "@tiptap/extension-hard-break" "^2.11.7"
    "@tiptap/extension-heading" "^2.11.7"
    "@tiptap/extension-history" "^2.11.7"
    "@tiptap/extension-horizontal-rule" "^2.11.7"
    "@tiptap/extension-italic" "^2.11.7"
    "@tiptap/extension-list-item" "^2.11.7"
    "@tiptap/extension-ordered-list" "^2.11.7"
    "@tiptap/extension-paragraph" "^2.11.7"
    "@tiptap/extension-strike" "^2.11.7"
    "@tiptap/extension-text" "^2.11.7"
    "@tiptap/extension-text-style" "^2.11.7"
    "@tiptap/pm" "^2.11.7"

"@tootallnate/once@2":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
  integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==

"@ts-morph/common@~0.19.0":
  version "0.19.0"
  resolved "https://registry.npmjs.org/@ts-morph/common/-/common-0.19.0.tgz"
  integrity sha512-Unz/WHmd4pGax91rdIKWi51wnVUW11QttMEPpBiBgIewnc9UQIX7UDLxr5vRlqeByXCwhkF6VabSsI0raWcyAQ==
  dependencies:
    fast-glob "^3.2.12"
    minimatch "^7.4.3"
    mkdirp "^2.1.6"
    path-browserify "^1.0.1"

"@types/bcryptjs@^2.4.6":
  version "2.4.6"
  resolved "https://registry.npmjs.org/@types/bcryptjs/-/bcryptjs-2.4.6.tgz"
  integrity sha512-9xlo6R2qDs5uixm0bcIqCeMCE6HiQsIyel9KQySStiyqNl2tnj2mP3DX1Nf56MD6KMenNNlBBsy3LJ7gUEQPXQ==

"@types/caseless@*":
  version "0.12.5"
  resolved "https://registry.npmjs.org/@types/caseless/-/caseless-0.12.5.tgz"
  integrity sha512-hWtVTC2q7hc7xZ/RLbxapMvDMgUnDvKvMOpKal4DrMyfGBUfB1oKaZlIRr6mJL+If3bAP6sV/QneGzF6tJjZDg==

"@types/cookie@^0.6.0":
  version "0.6.0"
  resolved "https://registry.npmjs.org/@types/cookie/-/cookie-0.6.0.tgz"
  integrity sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==

"@types/d3-array@^3.0.3":
  version "3.2.1"
  resolved "https://registry.npmjs.org/@types/d3-array/-/d3-array-3.2.1.tgz"
  integrity sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==

"@types/d3-color@*":
  version "3.1.3"
  resolved "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz"
  integrity sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==

"@types/d3-drag@^3.0.7":
  version "3.0.7"
  resolved "https://registry.npmjs.org/@types/d3-drag/-/d3-drag-3.0.7.tgz"
  integrity sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-ease@^3.0.0":
  version "3.0.2"
  resolved "https://registry.npmjs.org/@types/d3-ease/-/d3-ease-3.0.2.tgz"
  integrity sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==

"@types/d3-interpolate@*", "@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz"
  integrity sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.1.tgz"
  integrity sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==

"@types/d3-scale@^4.0.2":
  version "4.0.9"
  resolved "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.9.tgz"
  integrity sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==
  dependencies:
    "@types/d3-time" "*"

"@types/d3-selection@*", "@types/d3-selection@^3.0.10":
  version "3.0.11"
  resolved "https://registry.npmjs.org/@types/d3-selection/-/d3-selection-3.0.11.tgz"
  integrity sha512-bhAXu23DJWsrI45xafYpkQ4NtcKMwWnAC/vKrd2l+nxMFuvOT3XMYTIj2opv8vq8AO5Yh7Qac/nSeP/3zjTK0w==

"@types/d3-shape@^3.1.0":
  version "3.1.7"
  resolved "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.7.tgz"
  integrity sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz"
  integrity sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==

"@types/d3-timer@^3.0.0":
  version "3.0.2"
  resolved "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-3.0.2.tgz"
  integrity sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==

"@types/d3-transition@^3.0.8":
  version "3.0.9"
  resolved "https://registry.npmjs.org/@types/d3-transition/-/d3-transition-3.0.9.tgz"
  integrity sha512-uZS5shfxzO3rGlu0cC3bjmMFKsXv+SmZZcgp0KD22ts4uGXp5EVYGzu/0YdwZeKmddhcAccYtREJKkPfXkZuCg==
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-zoom@^3.0.8":
  version "3.0.8"
  resolved "https://registry.npmjs.org/@types/d3-zoom/-/d3-zoom-3.0.8.tgz"
  integrity sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==
  dependencies:
    "@types/d3-interpolate" "*"
    "@types/d3-selection" "*"

"@types/dagre@^0.7.52":
  version "0.7.52"
  resolved "https://registry.npmjs.org/@types/dagre/-/dagre-0.7.52.tgz"
  integrity sha512-XKJdy+OClLk3hketHi9Qg6gTfe1F3y+UFnHxKA2rn9Dw+oXa4Gb378Ztz9HlMgZKSxpPmn4BNVh9wgkpvrK1uw==

"@types/debug@^4.0.0":
  version "4.1.12"
  resolved "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz"
  integrity sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==
  dependencies:
    "@types/ms" "*"

"@types/diff-match-patch@^1.0.36":
  version "1.0.36"
  resolved "https://registry.npmjs.org/@types/diff-match-patch/-/diff-match-patch-1.0.36.tgz"
  integrity sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg==

"@types/dom-to-image@^2.6.7":
  version "2.6.7"
  resolved "https://registry.npmjs.org/@types/dom-to-image/-/dom-to-image-2.6.7.tgz"
  integrity sha512-me5VbCv+fcXozblWwG13krNBvuEOm6kA5xoa4RrjDJCNFOZSWR3/QLtOXimBHk1Fisq69Gx3JtOoXtg1N1tijg==

"@types/duplexify@*":
  version "3.6.4"
  resolved "https://registry.npmjs.org/@types/duplexify/-/duplexify-3.6.4.tgz"
  integrity sha512-2eahVPsd+dy3CL6FugAzJcxoraWhUghZGEQJns1kTKfCXWKJ5iG/VkaB05wRVrDKHfOFKqb0X0kXh91eE99RZg==
  dependencies:
    "@types/node" "*"

"@types/estree-jsx@^1.0.0":
  version "1.0.5"
  resolved "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz"
  integrity sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz"
  integrity sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==

"@types/file-saver@^2.0.7":
  version "2.0.7"
  resolved "https://registry.npmjs.org/@types/file-saver/-/file-saver-2.0.7.tgz"
  integrity sha512-dNKVfHd/jk0SkR/exKGj2ggkB45MAkzvWCaqLUUgkyjITkGNzH8H+yUwr+BLJUBjZOe9w8X3wgmXhZDRg1ED6A==

"@types/hast@^3.0.0":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz"
  integrity sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==
  dependencies:
    "@types/unist" "*"

"@types/linkify-it@^3":
  version "3.0.5"
  resolved "https://registry.npmjs.org/@types/linkify-it/-/linkify-it-3.0.5.tgz"
  integrity sha512-yg6E+u0/+Zjva+buc3EIb+29XEg4wltq7cSmd4Uc2EE/1nUVmxyzpX6gUXD0V8jIrG0r7YeOGVIbYRkxeooCtw==

"@types/linkify-it@^5":
  version "5.0.0"
  resolved "https://registry.npmjs.org/@types/linkify-it/-/linkify-it-5.0.0.tgz"
  integrity sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==

"@types/lodash@^4.17.10":
  version "4.17.16"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.16.tgz"
  integrity sha512-HX7Em5NYQAXKW+1T+FiuG27NGwzJfCX3s1GjOa7ujxZa52kjJLOr4FUxT+giF6Tgxv1e+/czV/iTtBw27WTU9g==

"@types/long@^5.0.0":
  version "5.0.0"
  resolved "https://registry.npmjs.org/@types/long/-/long-5.0.0.tgz"
  integrity sha512-eQs9RsucA/LNjnMoJvWG/nXa7Pot/RbBzilF/QRIU/xRl+0ApxrSUFsV5lmf01SvSlqMzJ7Zwxe440wmz2SJGA==
  dependencies:
    long "*"

"@types/markdown-it@^13.0.7":
  version "13.0.9"
  resolved "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-13.0.9.tgz"
  integrity sha512-1XPwR0+MgXLWfTn9gCsZ55AHOKW1WN+P9vr0PaQh5aerR9LLQXUbjfEAFhjmEmyoYFWAyuN2Mqkn40MZ4ukjBw==
  dependencies:
    "@types/linkify-it" "^3"
    "@types/mdurl" "^1"

"@types/markdown-it@^14.0.0":
  version "14.1.2"
  resolved "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-14.1.2.tgz"
  integrity sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==
  dependencies:
    "@types/linkify-it" "^5"
    "@types/mdurl" "^2"

"@types/mdast@^4.0.0":
  version "4.0.4"
  resolved "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz"
  integrity sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==
  dependencies:
    "@types/unist" "*"

"@types/mdurl@^1":
  version "1.0.5"
  resolved "https://registry.npmjs.org/@types/mdurl/-/mdurl-1.0.5.tgz"
  integrity sha512-6L6VymKTzYSrEf4Nev4Xa1LCHKrlTlYCBMTlQKFuddo1CvQcE52I0mwfOJayueUC7MJuXOeHTcIU683lzd0cUA==

"@types/mdurl@^2":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@types/mdurl/-/mdurl-2.0.0.tgz"
  integrity sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==

"@types/ms@*":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz"
  integrity sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==

"@types/node-fetch@^2.6.4":
  version "2.6.12"
  resolved "https://registry.npmjs.org/@types/node-fetch/-/node-fetch-2.6.12.tgz"
  integrity sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==
  dependencies:
    "@types/node" "*"
    form-data "^4.0.0"

"@types/node@*", "@types/node@20.10.6", "@types/node@>=13.7.0":
  version "20.10.6"
  resolved "https://registry.npmjs.org/@types/node/-/node-20.10.6.tgz"
  integrity sha512-Vac8H+NlRNNlAmDfGUP7b5h/KA+AtWIzuXy0E6OyP8f1tCLYAtPvKRRDJjAPqhpCb0t6U2j7/xqAuLEebW2kiw==
  dependencies:
    undici-types "~5.26.4"

"@types/node@^18.11.18":
  version "18.19.86"
  resolved "https://registry.npmjs.org/@types/node/-/node-18.19.86.tgz"
  integrity sha512-fifKayi175wLyKyc5qUfyENhQ1dCNI1UNjp653d8kuYcPQN5JhX3dGuP/XmvPTg/xRBn1VTLpbmi+H/Mr7tLfQ==
  dependencies:
    undici-types "~5.26.4"

"@types/phoenix@^1.5.4":
  version "1.6.6"
  resolved "https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz"
  integrity sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==

"@types/pumpify@^1.4.4":
  version "1.4.4"
  resolved "https://registry.npmjs.org/@types/pumpify/-/pumpify-1.4.4.tgz"
  integrity sha512-+cWbQUecD04MQYkjNBhPmcUIP368aloYmqm+ImdMKA8rMpxRNAhZAD6gIj+sAVTF1DliqrT/qUp6aGNi/9U3tw==
  dependencies:
    "@types/duplexify" "*"
    "@types/node" "*"

"@types/raf@^3.4.0":
  version "3.4.3"
  resolved "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz"
  integrity sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw==

"@types/react-dom@npm:types-react-dom@19.0.0-rc.1":
  version "19.0.0-rc.1"
  resolved "https://registry.npmjs.org/types-react-dom/-/types-react-dom-19.0.0-rc.1.tgz"
  integrity sha512-VSLZJl8VXCD0fAWp7DUTFUDCcZ8DVXOQmjhJMD03odgeFmu14ZQJHCXeETm3BEAhJqfgJaFkLnGkQv88sRx0fQ==
  dependencies:
    "@types/react" "*"

"@types/react-grid-layout@^1.3.5":
  version "1.3.5"
  resolved "https://registry.npmjs.org/@types/react-grid-layout/-/react-grid-layout-1.3.5.tgz"
  integrity sha512-WH/po1gcEcoR6y857yAnPGug+ZhkF4PaTUxgAbwfeSH/QOgVSakKHBXoPGad/sEznmkiaK3pqHk+etdWisoeBQ==
  dependencies:
    "@types/react" "*"

"@types/react-rnd@^7.4.4":
  version "7.4.4"
  resolved "https://registry.npmjs.org/@types/react-rnd/-/react-rnd-7.4.4.tgz"
  integrity sha512-Rh9UdKFiV4fxh/05shQsU3RO+eH5anU6eZCo8NBzobEfB6QU50ISLVP24rEZtIXbYsiiwjo7w960aAZ6JicpMg==
  dependencies:
    "@types/react" "*"

"@types/react-signature-canvas@^1.0.6":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@types/react-signature-canvas/-/react-signature-canvas-1.0.7.tgz"
  integrity sha512-0ulzaUvcIQ0HdNB5fHj+KE7ztWhlhYRsi65TdPIRj/t+FD5Rr8NJKBv4/xLViz7HsUh/tgqsoyKeARrm9+gPIg==
  dependencies:
    "@types/react" "*"
    "@types/signature_pad" "<3"

"@types/react-table@^7.7.20":
  version "7.7.20"
  resolved "https://registry.npmjs.org/@types/react-table/-/react-table-7.7.20.tgz"
  integrity sha512-ahMp4pmjVlnExxNwxyaDrFgmKxSbPwU23sGQw2gJK4EhCvnvmib2s/O/+y1dfV57dXOwpr2plfyBol+vEHbi2w==
  dependencies:
    "@types/react" "*"

"@types/react@*":
  version "19.1.5"
  resolved "https://registry.yarnpkg.com/@types/react/-/react-19.1.5.tgz#9feb3bdeb506d0c79d8533b6ebdcacdbcb4756db"
  integrity sha512-piErsCVVbpMMT2r7wbawdZsq4xMvIAhQuac2gedQHysu1TZYEigE6pnFfgZT+/jQnrRuF5r+SHzuehFjfRjr4g==
  dependencies:
    csstype "^3.0.2"

"@types/react@npm:types-react@19.0.0-rc.1":
  version "19.0.0-rc.1"
  resolved "https://registry.npmjs.org/types-react/-/types-react-19.0.0-rc.1.tgz"
  integrity sha512-RshndUfqTW6K3STLPis8BtAYCGOkMbtvYsi90gmVNDZBXUyUc5juf2PE9LfS/JmOlUIRO8cWTS/1MTnmhjDqyQ==
  dependencies:
    csstype "^3.0.2"

"@types/regression@^2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@types/regression/-/regression-2.0.6.tgz"
  integrity sha512-sa+sHOUxh9fywFuAFLCcyupFN0CKX654QUZGW5fAZCmV51I4e5nQy1xL2g/JMUW/PeDoF3Yq2lDXb7MoC3KDNg==

"@types/request@^2.48.12":
  version "2.48.12"
  resolved "https://registry.npmjs.org/@types/request/-/request-2.48.12.tgz"
  integrity sha512-G3sY+NpsA9jnwm0ixhAFQSJ3Q9JkpLZpJbI3GMv0mIAT0y3mRabYeINzal5WOChIiaTEGQYlHOKgkaM9EisWHw==
  dependencies:
    "@types/caseless" "*"
    "@types/node" "*"
    "@types/tough-cookie" "*"
    form-data "^2.5.0"

"@types/signature_pad@<3":
  version "2.3.6"
  resolved "https://registry.npmjs.org/@types/signature_pad/-/signature_pad-2.3.6.tgz"
  integrity sha512-v3j92gCQJoxomHhd+yaG4Vsf8tRS/XbzWKqDv85UsqjMGy4zhokuwKe4b6vhbgncKkh+thF+gpz6+fypTtnFqQ==

"@types/statuses@^2.0.4":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@types/statuses/-/statuses-2.0.5.tgz"
  integrity sha512-jmIUGWrAiwu3dZpxntxieC+1n/5c3mjrImkmOSQ2NC5uP6cYO4aAZDdSmRcI5C1oiTmqlZGHC+/NmJrKogbP5A==

"@types/tough-cookie@*", "@types/tough-cookie@^4.0.5":
  version "4.0.5"
  resolved "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz"
  integrity sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz"
  integrity sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==

"@types/unist@^2.0.0":
  version "2.0.11"
  resolved "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz"
  integrity sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==

"@types/use-sync-external-store@^0.0.6":
  version "0.0.6"
  resolved "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz"
  integrity sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg==

"@types/uuid@^10.0.0":
  version "10.0.0"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==

"@types/ws@^8.5.10":
  version "8.18.1"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz"
  integrity sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==
  dependencies:
    "@types/node" "*"

"@ungap/structured-clone@^1.0.0":
  version "1.3.0"
  resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  integrity sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==

"@vercel/analytics@^1.4.1":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@vercel/analytics/-/analytics-1.5.0.tgz"
  integrity sha512-MYsBzfPki4gthY5HnYN7jgInhAZ7Ac1cYDoRWFomwGHWEX7odTEzbtg9kf/QSo7XEsEAqlQugA6gJ2WS2DEa3g==

"@vercel/speed-insights@^1.1.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@vercel/speed-insights/-/speed-insights-1.2.0.tgz"
  integrity sha512-y9GVzrUJ2xmgtQlzFP2KhVRoCglwfRQgjyfY607aU0hh0Un6d0OUyrJkjuAlsV18qR4zfoFPs/BiIj9YDS6Wzw==

"@xyflow/react@^12.3.6":
  version "12.5.6"
  resolved "https://registry.npmjs.org/@xyflow/react/-/react-12.5.6.tgz"
  integrity sha512-a6lL0WoeMSp7AC9AQzWMRMuqk12Dn+lVjMDLL93SZvpWv5D2BSq9woCv21JCUdWQ31MNpJVfLaV3TycaH1tsYw==
  dependencies:
    "@xyflow/system" "0.0.56"
    classcat "^5.0.3"
    zustand "^4.4.0"

"@xyflow/system@0.0.56":
  version "0.0.56"
  resolved "https://registry.npmjs.org/@xyflow/system/-/system-0.0.56.tgz"
  integrity sha512-Xc3LvEumjJD+CqPqlYkrlszJ4hWQ0DE+r5M4e5WpS/hKT4T6ktAjt7zeMNJ+vvTsXHulGnEoDRA8zbIfB6tPdQ==
  dependencies:
    "@types/d3-drag" "^3.0.7"
    "@types/d3-selection" "^3.0.10"
    "@types/d3-transition" "^3.0.8"
    "@types/d3-zoom" "^3.0.8"
    d3-drag "^3.0.0"
    d3-selection "^3.0.0"
    d3-zoom "^3.0.0"

"@yr/monotone-cubic-spline@^1.0.3":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@yr/monotone-cubic-spline/-/monotone-cubic-spline-1.0.3.tgz"
  integrity sha512-FQXkOta0XBSUPHndIKON2Y9JeQz5ZeMqLYZVVK93FliNBFm7LNMIZmY6FrMEB9XPcDbE2bekMbZD6kzDkxwYjA==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

abs-svg-path@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  integrity sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA==

adler-32@~1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/adler-32/-/adler-32-1.3.1.tgz"
  integrity sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

agent-base@^7.0.2, agent-base@^7.1.2:
  version "7.1.3"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==

agentkeepalive@^4.2.1:
  version "4.6.0"
  resolved "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz"
  integrity sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==
  dependencies:
    humanize-ms "^1.2.1"

ai@^4.3.11:
  version "4.3.11"
  resolved "https://registry.npmjs.org/ai/-/ai-4.3.11.tgz"
  integrity sha512-5korDAKCsDXE5HwglOJ7NxJ32TEj2NmKGQPefcMJMKkjxeJJjC6NfM8qLtPCCk8FoAVtYdIMYPy8dzux423QLg==
  dependencies:
    "@ai-sdk/provider" "1.1.3"
    "@ai-sdk/provider-utils" "2.2.7"
    "@ai-sdk/react" "1.2.10"
    "@ai-sdk/ui-utils" "1.2.9"
    "@opentelemetry/api" "1.9.0"
    jsondiffpatch "0.6.0"

ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

apexcharts@^4.1.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/apexcharts/-/apexcharts-4.5.0.tgz"
  integrity sha512-E7ZkrVqPNBUWy/Rmg8DEIqHNBmElzICE/oxOX5Ekvs2ICQUOK/VkEkMH09JGJu+O/EA0NL31hxlmF+wrwrSLaQ==
  dependencies:
    "@svgdotjs/svg.draggable.js" "^3.0.4"
    "@svgdotjs/svg.filter.js" "^3.0.8"
    "@svgdotjs/svg.js" "^3.2.4"
    "@svgdotjs/svg.resize.js" "^2.0.2"
    "@svgdotjs/svg.select.js" "^4.0.1"
    "@yr/monotone-cubic-spline" "^1.0.3"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-hidden@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.4.tgz"
  integrity sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==
  dependencies:
    tslib "^2.0.0"

arrify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/arrify/-/arrify-2.0.1.tgz"
  integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==

ast-types@^0.16.1:
  version "0.16.1"
  resolved "https://registry.npmjs.org/ast-types/-/ast-types-0.16.1.tgz"
  integrity sha512-6t10qk83GOG8p0vKmaCr8eiilZwO171AvbROMtvvNiwrTly62t+7XkA8RdIIVbpMhCASAsxgAzdRSwh6nw/5Dg==
  dependencies:
    tslib "^2.0.1"

async@^0.2.9:
  version "0.2.10"
  resolved "https://registry.npmjs.org/async/-/async-0.2.10.tgz"
  integrity sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==

attr-accept@^2.2.4:
  version "2.2.5"
  resolved "https://registry.npmjs.org/attr-accept/-/attr-accept-2.2.5.tgz"
  integrity sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==

autoprefixer@10.4.17:
  version "10.4.17"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.17.tgz"
  integrity sha512-/********************************+erw5ifnMLZb0UnSlkK4tquLmkd3BhA+nLo5tX8Cu0upUsGKvKbmg==
  dependencies:
    browserslist "^4.22.2"
    caniuse-lite "^1.0.30001578"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

bail@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz"
  integrity sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  integrity sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==

base64-js@0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz"
  integrity sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==

base64-js@^1.1.2, base64-js@^1.3.0, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bcryptjs@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.2.tgz"
  integrity sha512-k38b3XOZKv60C4E2hVsXTolJWfkGRMbILBIe2IBITXciy5bOsTKot5kDrf3ZfufQtQOUN5mXceUEpU1rTl9Uog==

bidi-js@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/bidi-js/-/bidi-js-1.0.3.tgz"
  integrity sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==
  dependencies:
    require-from-string "^2.0.2"

bignumber.js@^9.0.0:
  version "9.2.1"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.2.1.tgz"
  integrity sha512-+NzaKgOUvInq9TIUZ1+DRspzf/HApkCwD4btfuasFTdrfnOxqx853TgDpMolp+uv4RpRp7bPcEU2zKr9+fRmyw==

bin-links@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/bin-links/-/bin-links-5.0.0.tgz"
  integrity sha512-sdleLVfCjBtgO5cNjA2HVRvWBJAHs4zwenaCPMNJAJU0yNxpzj80IpjOIimkpkr+mhlA+how5poQtt53PygbHA==
  dependencies:
    cmd-shim "^7.0.0"
    npm-normalize-package-bin "^4.0.0"
    proc-log "^5.0.0"
    read-cmd-shim "^5.0.0"
    write-file-atomic "^6.0.0"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bl@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/bl/-/bl-5.1.0.tgz"
  integrity sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==
  dependencies:
    buffer "^6.0.3"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

brotli@^1.3.2:
  version "1.3.3"
  resolved "https://registry.npmjs.org/brotli/-/brotli-1.3.3.tgz"
  integrity sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==
  dependencies:
    base64-js "^1.1.2"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz"
  integrity sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
  dependencies:
    pako "~1.0.5"

browserslist@^4.22.2, browserslist@^4.24.0:
  version "4.24.4"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz"
  integrity sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

btoa@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  integrity sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

busboy@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

caniuse-lite@^1.0.30001578, caniuse-lite@^1.0.30001579, caniuse-lite@^1.0.30001688, caniuse-lite@^1.0.30001712:
  version "1.0.30001713"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001713.tgz"
  integrity sha512-wCIWIg+A4Xr7NfhTuHdX+/FKh3+Op3LBbSp2N5Pfx6T/LhdQy3GTyoTg48BReaW/MyMNZAkTadsBtai3ldWK0Q==

canvg@^3.0.6:
  version "3.0.11"
  resolved "https://registry.npmjs.org/canvg/-/canvg-3.0.11.tgz"
  integrity sha512-5ON+q7jCTgMp9cjpu4Jo6XbvfYwSB2Ow3kzHKfIyJfaCAOHLbdKPQqGKgfED/R5B+3TFFfe8pegYA+b423SRyA==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    core-js "^3.8.3"
    raf "^3.4.1"
    regenerator-runtime "^0.13.7"
    rgbcolor "^1.0.1"
    stackblur-canvas "^2.0.0"
    svg-pathdata "^6.0.3"

ccount@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz"
  integrity sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==

cfb@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/cfb/-/cfb-1.2.2.tgz"
  integrity sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@^5.0.0, chalk@^5.3.0:
  version "5.4.1"
  resolved "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz"
  integrity sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==

character-entities-html4@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz"
  integrity sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==

character-entities-legacy@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz"
  integrity sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==

character-entities@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz"
  integrity sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==

character-reference-invalid@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz"
  integrity sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz"
  integrity sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==

class-variance-authority@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  integrity sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==
  dependencies:
    clsx "^2.1.1"

classcat@^5.0.3:
  version "5.0.5"
  resolved "https://registry.npmjs.org/classcat/-/classcat-5.0.5.tgz"
  integrity sha512-JhZUT7JFcQy/EzW605k/ktHtncoo9vnyW/2GspNYwFlN1C/WmjuV/xtS04e9SOkL2sTdw0VAZ2UGCcQ9lR6p6w==

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-4.0.0.tgz"
  integrity sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==
  dependencies:
    restore-cursor "^4.0.0"

cli-spinners@^2.6.1:
  version "2.9.2"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

cli-width@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz"
  integrity sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==

client-only@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz"
  integrity sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

clone@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz"
  integrity sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==

clsx@^1.1.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^2.0.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

cmd-shim@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/cmd-shim/-/cmd-shim-7.0.0.tgz"
  integrity sha512-rtpaCbr164TPPh+zFdkWpCyZuKkjpAzODfaZCf/SVJZzJN+4bHQb/LP3Jzq5/+84um3XXY8r548XiWKSborwVw==

cmdk@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/cmdk/-/cmdk-1.1.1.tgz"
  integrity sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg==
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

code-block-writer@^12.0.0:
  version "12.0.0"
  resolved "https://registry.npmjs.org/code-block-writer/-/code-block-writer-12.0.0.tgz"
  integrity sha512-q4dMFMlXtKR3XNBHyMHt/3pwYNA69EDk00lloMOaaUMKPUXBw6lpXtbu3MMVG6/uOihGnRDOlkyqsONEUj60+w==

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/codepage/-/codepage-1.15.0.tgz"
  integrity sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

comma-separated-tokens@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz"
  integrity sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==

commander@^10.0.0:
  version "10.0.1"
  resolved "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz"
  integrity sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie@^0.7.2:
  version "0.7.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz"
  integrity sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==

cookie@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz"
  integrity sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==

core-js@^3.6.0, core-js@^3.8.3:
  version "3.41.0"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.41.0.tgz"
  integrity sha512-SJ4/EHwS36QMJd6h/Rg+GyR4A5xE0FSI3eZ+iBVpfqf1x0eTSg1smWLHrA+2jQThZSh97fmSgFSU8B61nxosxA==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

crelt@^1.0.0:
  version "1.0.6"
  resolved "https://registry.npmjs.org/crelt/-/crelt-1.0.6.tgz"
  integrity sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==

cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  integrity sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==
  dependencies:
    utrie "^1.0.2"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

"d3-array@2 - 3", "d3-array@2.10.0 - 3", d3-array@^3.1.6:
  version "3.2.4"
  resolved "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz"
  integrity sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz"
  integrity sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==

"d3-dispatch@1 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-3.0.1.tgz"
  integrity sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==

"d3-drag@2 - 3", d3-drag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-drag/-/d3-drag-3.0.0.tgz"
  integrity sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==
  dependencies:
    d3-dispatch "1 - 3"
    d3-selection "3"

"d3-ease@1 - 3", d3-ease@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz"
  integrity sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==

"d3-format@1 - 3":
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz"
  integrity sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==

"d3-interpolate@1 - 3", "d3-interpolate@1.2.0 - 3", d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz"
  integrity sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz"
  integrity sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

"d3-selection@2 - 3", d3-selection@3, d3-selection@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-selection/-/d3-selection-3.0.0.tgz"
  integrity sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==

d3-shape@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz"
  integrity sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz"
  integrity sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==
  dependencies:
    d3-time "1 - 3"

"d3-time@1 - 3", "d3-time@2.1.1 - 3", d3-time@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz"
  integrity sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==
  dependencies:
    d3-array "2 - 3"

"d3-timer@1 - 3", d3-timer@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz"
  integrity sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==

"d3-transition@2 - 3":
  version "3.0.1"
  resolved "https://registry.npmjs.org/d3-transition/-/d3-transition-3.0.1.tgz"
  integrity sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==
  dependencies:
    d3-color "1 - 3"
    d3-dispatch "1 - 3"
    d3-ease "1 - 3"
    d3-interpolate "1 - 3"
    d3-timer "1 - 3"

d3-zoom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/d3-zoom/-/d3-zoom-3.0.0.tgz"
  integrity sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==
  dependencies:
    d3-dispatch "1 - 3"
    d3-drag "2 - 3"
    d3-interpolate "1 - 3"
    d3-selection "2 - 3"
    d3-transition "2 - 3"

dagre@^0.8.5:
  version "0.8.5"
  resolved "https://registry.npmjs.org/dagre/-/dagre-0.8.5.tgz"
  integrity sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw==
  dependencies:
    graphlib "^2.1.8"
    lodash "^4.17.15"

data-uri-to-buffer@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz"
  integrity sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==

date-fns-tz@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/date-fns-tz/-/date-fns-tz-3.2.0.tgz"
  integrity sha512-sg8HqoTEulcbbbVXeg84u5UnlsQa8GS5QXMqjjYIhS4abEVVKIUwe0/l/UhrZdKaL/W5eWZNlbTeEIiOXTcsBQ==

date-fns@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz"
  integrity sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==

debug@4, debug@^4.0.0, debug@^4.1.0, debug@^4.3.1:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.4.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/decimal.js-light/-/decimal.js-light-2.5.1.tgz"
  integrity sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==

decode-named-character-reference@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.1.0.tgz"
  integrity sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w==
  dependencies:
    character-entities "^2.0.0"

deepmerge@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

dequal@^2.0.0, dequal@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==

devlop@^1.0.0, devlop@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz"
  integrity sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==
  dependencies:
    dequal "^2.0.0"

dfa@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/dfa/-/dfa-1.2.0.tgz"
  integrity sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

diff-match-patch@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/diff-match-patch/-/diff-match-patch-1.0.5.tgz"
  integrity sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==

diff@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/diff/-/diff-5.2.0.tgz"
  integrity sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

dnd-core@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/dnd-core/-/dnd-core-16.0.1.tgz"
  integrity sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==
  dependencies:
    "@react-dnd/asap" "^5.0.1"
    "@react-dnd/invariant" "^4.0.1"
    redux "^4.2.0"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/dom-serializer/-/dom-serializer-2.0.0.tgz"
  integrity sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom-to-image@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/dom-to-image/-/dom-to-image-2.6.0.tgz"
  integrity sha512-Dt0QdaHmLpjURjU7Tnu3AgYSF2LuOmksSGsUcE6ItvJoCWTBEmiMXcqBdNSAm9+QbbwD7JMoVsuuKX6ZVQv1qA==

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz"
  integrity sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
  dependencies:
    domelementtype "^2.3.0"

dompurify@^2.5.4:
  version "2.5.8"
  resolved "https://registry.npmjs.org/dompurify/-/dompurify-2.5.8.tgz"
  integrity sha512-o1vSNgrmYMQObbSSvF/1brBYEQPHhV1+gsmrusO7/GXtp1T9rCS8cXFqVxK/9crT1jA6Ccv+5MTSjBNqr7Sovw==

domutils@^3.0.1:
  version "3.2.2"
  resolved "https://registry.npmjs.org/domutils/-/domutils-3.2.2.tgz"
  integrity sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexify@^4.1.1, duplexify@^4.1.3:
  version "4.1.3"
  resolved "https://registry.npmjs.org/duplexify/-/duplexify-4.1.3.tgz"
  integrity sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==
  dependencies:
    end-of-stream "^1.4.1"
    inherits "^2.0.3"
    readable-stream "^3.1.1"
    stream-shift "^1.0.2"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

ecdsa-sig-formatter@1.0.11, ecdsa-sig-formatter@^1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

electron-to-chromium@^1.5.73:
  version "1.5.137"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.137.tgz"
  integrity sha512-/QSJaU2JyIuTbbABAo/crOs+SuAZLS+fVVS10PVrIT9hrRkmZl8Hb0xPSkKRUUWHQtYzXHpQUW3Dy5hwMzGZkA==

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-10.4.0.tgz"
  integrity sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

entities@^4.2.0, entities@^4.4.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

esprima@~4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

estree-util-is-identifier-name@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz"
  integrity sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

eventemitter3@^4.0.1:
  version "4.0.7"
  resolved "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/execa/-/execa-7.2.0.tgz"
  integrity sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

extend@^3.0.0, extend@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz"
  integrity sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==

fast-deep-equal@^3, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-equals@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.3.tgz"
  integrity sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg==

fast-equals@^5.0.1:
  version "5.2.2"
  resolved "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2.tgz"
  integrity sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==

fast-glob@^3.2.12, fast-glob@^3.3.0, fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz"
  integrity sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==
  dependencies:
    reusify "^1.0.4"

fetch-blob@^3.1.2, fetch-blob@^3.1.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz"
  integrity sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==
  dependencies:
    node-domexception "^1.0.0"
    web-streams-polyfill "^3.0.3"

fflate@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  integrity sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A==

file-saver@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/file-saver/-/file-saver-2.0.5.tgz"
  integrity sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA==

file-selector@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/file-selector/-/file-selector-2.1.2.tgz"
  integrity sha512-QgXo+mXTe8ljeqUFaX3QVHc5osSItJ/Km+xpocx0aSqWGMSCf6qYs/VnzZgS864Pjn5iceMRFigeAV7AfTlaig==
  dependencies:
    tslib "^2.7.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

fluent-ffmpeg@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/fluent-ffmpeg/-/fluent-ffmpeg-2.1.3.tgz"
  integrity sha512-Be3narBNt2s6bsaqP6Jzq91heDgOEaDCJAXcE3qcma/EJBSy5FB4cvO31XBInuAuKBx8Kptf8dkhjK0IOru39Q==
  dependencies:
    async "^0.2.9"
    which "^1.1.1"

fontkit@^2.0.2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/fontkit/-/fontkit-2.0.4.tgz"
  integrity sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==
  dependencies:
    "@swc/helpers" "^0.5.12"
    brotli "^1.3.2"
    clone "^2.1.2"
    dfa "^1.2.0"
    fast-deep-equal "^3.1.3"
    restructure "^3.0.0"
    tiny-inflate "^1.0.3"
    unicode-properties "^1.4.0"
    unicode-trie "^2.0.0"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz"
  integrity sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data-encoder@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz"
  integrity sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==

form-data@^2.5.0:
  version "2.5.3"
  resolved "https://registry.npmjs.org/form-data/-/form-data-2.5.3.tgz"
  integrity sha512-XHIrMD0NpDrNM/Ckf7XJiBbLl57KEhT3+i3yY+eWm+cqYZJQTZrKo8Y8AWKnuV5GT4scfuUGt9LzNoIx3dU1nQ==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.35"
    safe-buffer "^5.2.1"

form-data@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz"
  integrity sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.12"

formdata-node@^4.3.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz"
  integrity sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==
  dependencies:
    node-domexception "1.0.0"
    web-streams-polyfill "4.0.0-beta.3"

formdata-polyfill@^4.0.10:
  version "4.0.10"
  resolved "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz"
  integrity sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==
  dependencies:
    fetch-blob "^3.1.2"

frac@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/frac/-/frac-1.1.2.tgz"
  integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

framer-motion@^11.11.9:
  version "11.18.2"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-11.18.2.tgz"
  integrity sha512-5F5Och7wrvtLVElIpclDT0CBzMVg3dL22B64aZwHtsIY8RB4mXICLrkajK4G9R+ieSAGcgrLeae2SeUTg2pr6w==
  dependencies:
    motion-dom "^11.18.1"
    motion-utils "^11.18.1"
    tslib "^2.4.0"

fs-extra@^11.1.0:
  version "11.3.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-11.3.0.tgz"
  integrity sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

gaxios@^7.0.0-rc.1, gaxios@^7.0.0-rc.4:
  version "7.0.0-rc.4"
  resolved "https://registry.npmjs.org/gaxios/-/gaxios-7.0.0-rc.4.tgz"
  integrity sha512-fwQMwbs3o8Odl/nc/rkQJwyHeOXdderOwmybUl0gkyTdZXMK1oSTWj4Em7gSogVJsRWDeHPXLY06+e8Rkr01iw==
  dependencies:
    extend "^3.0.2"
    https-proxy-agent "^7.0.1"
    node-fetch "^3.3.2"

gcp-metadata@^7.0.0-rc.1:
  version "7.0.0-rc.1"
  resolved "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-7.0.0-rc.1.tgz"
  integrity sha512-E6c+AdIaK1LNA839OyotiTca+B2IG1nDlMjnlcck8JjXn3fVgx57Ib9i6iL1/iqN7bA3EUQdcRRu+HqOCOABIg==
  dependencies:
    gaxios "^7.0.0-rc.1"
    google-logging-utils "^1.0.0"
    json-bigint "^1.0.0"

geist@^1.2.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/geist/-/geist-1.3.1.tgz"
  integrity sha512-Q4gC1pBVPN+D579pBaz0TRRnGA4p9UK6elDY/xizXdFk/g4EKR5g0I+4p/Kj6gM0SajDBZ/0FvDV9ey9ud7BWw==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.2.6:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==

get-own-enumerable-keys@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/get-own-enumerable-keys/-/get-own-enumerable-keys-1.0.0.tgz"
  integrity sha512-PKsK2FSrQCyxcGHsGrLDcK0lx+0Ke+6e8KFFozA9/fIQLhQzPaRvJFdcz7+Axg3jUH/Mq+NI4xa5u/UT2tQskA==

get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

google-auth-library@^10.0.0-rc.1:
  version "10.0.0-rc.1"
  resolved "https://registry.npmjs.org/google-auth-library/-/google-auth-library-10.0.0-rc.1.tgz"
  integrity sha512-Ri8Yk7bMhaPcqzwyW5XHS4scc5KL+AdyUVxA5YGw9BUxYcL2P/tdEfj13O9KpV03k5sUqlaTL+HPxb/deGVjxw==
  dependencies:
    base64-js "^1.3.0"
    ecdsa-sig-formatter "^1.0.11"
    gaxios "^7.0.0-rc.4"
    gcp-metadata "^7.0.0-rc.1"
    gtoken "^8.0.0-rc.1"
    jws "^4.0.0"

google-gax@^5.0.1-rc.0:
  version "5.0.1-rc.0"
  resolved "https://registry.npmjs.org/google-gax/-/google-gax-5.0.1-rc.0.tgz"
  integrity sha512-7dfJz21VjUYfVB8XKfXw2ETvjUp1Oa2DUFiNqnJ2k+AY0BDDeqIe4H5ROmLw8dRfv+TwmCL4DJELPRuOh8XAGA==
  dependencies:
    "@grpc/grpc-js" "^1.12.6"
    "@grpc/proto-loader" "^0.7.13"
    "@types/long" "^5.0.0"
    abort-controller "^3.0.0"
    duplexify "^4.1.3"
    google-auth-library "^10.0.0-rc.1"
    google-logging-utils "^1.1.1"
    node-fetch "^3.3.2"
    object-hash "^3.0.0"
    proto3-json-serializer "^3.0.0"
    protobufjs "^7.4.0"
    retry-request "^8.0.0"

google-logging-utils@^1.0.0, google-logging-utils@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/google-logging-utils/-/google-logging-utils-1.1.1.tgz"
  integrity sha512-rcX58I7nqpu4mbKztFeOAObbomBbHU2oIb/d3tJfF3dizGSApqtSwYJigGCooHdnMyQBIw8BrWyK96w3YXgr6A==

gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphlib@^2.1.8:
  version "2.1.8"
  resolved "https://registry.npmjs.org/graphlib/-/graphlib-2.1.8.tgz"
  integrity sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==
  dependencies:
    lodash "^4.17.15"

graphql@^16.8.1:
  version "16.10.0"
  resolved "https://registry.npmjs.org/graphql/-/graphql-16.10.0.tgz"
  integrity sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==

groq-sdk@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/groq-sdk/-/groq-sdk-0.7.0.tgz"
  integrity sha512-OgPqrRtti5MjEVclR8sgBHrhSkTLdFCmi47yrEF29uJZaiCkX3s7bXpnMhq8Lwoe1f4AwgC0qGOeHXpeSgu5lg==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

gtoken@^8.0.0-rc.1:
  version "8.0.0-rc.1"
  resolved "https://registry.npmjs.org/gtoken/-/gtoken-8.0.0-rc.1.tgz"
  integrity sha512-UjE/egX6ixArdcCKOkheuFQ4XN4/0gX92nd2JPVEYuRU2sWHAWuOVGnowm1fQUdQtaxqn1n8H0hOb2LCaUhJ3A==
  dependencies:
    gaxios "^7.0.0-rc.1"
    jws "^4.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hast-util-to-jsx-runtime@^2.0.0:
  version "2.3.6"
  resolved "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz"
  integrity sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    estree-util-is-identifier-name "^3.0.0"
    hast-util-whitespace "^3.0.0"
    mdast-util-mdx-expression "^2.0.0"
    mdast-util-mdx-jsx "^3.0.0"
    mdast-util-mdxjs-esm "^2.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"
    style-to-js "^1.0.0"
    unist-util-position "^5.0.0"
    vfile-message "^4.0.0"

hast-util-whitespace@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz"
  integrity sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==
  dependencies:
    "@types/hast" "^3.0.0"

headers-polyfill@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/headers-polyfill/-/headers-polyfill-4.0.3.tgz"
  integrity sha512-IScLbePpkvO846sIwOtOTDjutRMWdXdJmXdMvk6gCBHxFO8d+QKOQedyZSxFTTFYRSmlgSTDtXqqq4pcenBXLQ==

highlight.js@~11.11.0:
  version "11.11.1"
  resolved "https://registry.npmjs.org/highlight.js/-/highlight.js-11.11.1.tgz"
  integrity sha512-Xwwo44whKBVCYoliBQwaPvtd/2tYFkRQtXDWj1nackaV2JPXx3L0+Jvd8/qCJ2p+ML0/XVkJ2q+Mr+UVdpJK5w==

hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

hsl-to-hex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hsl-to-hex/-/hsl-to-hex-1.0.0.tgz"
  integrity sha512-K6GVpucS5wFf44X0h2bLVRDsycgJmf9FF2elg+CrqD8GcFU8c6vYhgXn8NjUkFCwj+xDFb70qgLbTUm6sxwPmA==
  dependencies:
    hsl-to-rgb-for-reals "^1.1.0"

hsl-to-rgb-for-reals@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/hsl-to-rgb-for-reals/-/hsl-to-rgb-for-reals-1.1.1.tgz"
  integrity sha512-LgOWAkrN0rFaQpfdWBQlv/VhkOxb5AsBjk6NQVx4yEzWS923T07X0M1Y0VNko2H52HeSpZrZNNMJ0aFqsdVzQg==

html-entities@^2.5.2:
  version "2.6.0"
  resolved "https://registry.npmjs.org/html-entities/-/html-entities-2.6.0.tgz"
  integrity sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==

html-to-text@9.0.5:
  version "9.0.5"
  resolved "https://registry.npmjs.org/html-to-text/-/html-to-text-9.0.5.tgz"
  integrity sha512-qY60FjREgVZL03vJU6IfMV4GDjGBIoOyvuFdpBDIX9yTlDw0TjxVBQp+P8NvpdIXNJvfWBTNul7fsAQJq2FNpg==
  dependencies:
    "@selderee/plugin-htmlparser2" "^0.11.0"
    deepmerge "^4.3.1"
    dom-serializer "^2.0.0"
    htmlparser2 "^8.0.2"
    selderee "^0.11.0"

html-url-attributes@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz"
  integrity sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==

html2canvas@^1.0.0-rc.5, html2canvas@^1.0.0-rc.7, html2canvas@^1.2.0, html2canvas@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  integrity sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

htmlparser2@^8.0.2:
  version "8.0.2"
  resolved "https://registry.npmjs.org/htmlparser2/-/htmlparser2-8.0.2.tgz"
  integrity sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

https-proxy-agent@^6.2.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-6.2.1.tgz"
  integrity sha512-ONsE3+yfZF2caH5+bJlcddtWqNI3Gvs5A38+ngvljxaBiRXRswym2c7yf8UAeFpRFKjFNHIFEHqR/OLAWJzyiA==
  dependencies:
    agent-base "^7.0.2"
    debug "4"

https-proxy-agent@^7.0.1, https-proxy-agent@^7.0.2:
  version "7.0.6"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^4.3.0:
  version "4.3.1"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-4.3.1.tgz"
  integrity sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
  dependencies:
    ms "^2.0.0"

hyphen@^1.6.4:
  version "1.10.6"
  resolved "https://registry.npmjs.org/hyphen/-/hyphen-1.10.6.tgz"
  integrity sha512-fXHXcGFTXOvZTSkPJuGOQf5Lv5T/R2itiiCVPg9LxAje5D00O0pP83yJShFq5V89Ly//Gt6acj7z8pbBr34stw==

ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

immediate@~3.0.5:
  version "3.0.6"
  resolved "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==

import-fresh@^3.3.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz"
  integrity sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inline-style-parser@0.2.4:
  version "0.2.4"
  resolved "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz"
  integrity sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz"
  integrity sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==

is-alphabetical@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz"
  integrity sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==

is-alphanumerical@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz"
  integrity sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==
  dependencies:
    is-alphabetical "^2.0.0"
    is-decimal "^2.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-decimal@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz"
  integrity sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz"
  integrity sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==

is-interactive@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-2.0.0.tgz"
  integrity sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==

is-node-process@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/is-node-process/-/is-node-process-1.2.0.tgz"
  integrity sha512-Vg4o6/fqPxIjtxgUH5QLJhwZ7gW5diGCVlXpuUfELC62CuxM1iHcRe51f2W1FDy04Ai4KJkagKjx3XaqyfRKXw==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-obj/-/is-obj-3.0.0.tgz"
  integrity sha512-IlsXEHOjtKhpN8r/tRFj2nDyTmHvcfNeu/nrRIcXE17ROeatXchkojffa1SpdqW4cr/Fj6QkEf/Gn4zf6KKvEQ==

is-plain-obj@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz"
  integrity sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==

is-regexp@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/is-regexp/-/is-regexp-3.1.0.tgz"
  integrity sha512-rbku49cWloU5bSMI+zaRaXdQHXnthP6DZ/vLnfdSKyL4zUzuWnomtOEiZZOd+ioQ+avFo/qau3KPTc7Fjy1uPA==

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz"
  integrity sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==

is-unicode-supported@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz"
  integrity sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==

is-url@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/is-url/-/is-url-1.2.4.tgz"
  integrity sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isomorphic.js@^0.2.4:
  version "0.2.5"
  resolved "https://registry.npmjs.org/isomorphic.js/-/isomorphic.js-0.2.5.tgz"
  integrity sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jay-peg@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/jay-peg/-/jay-peg-1.1.1.tgz"
  integrity sha512-D62KEuBxz/ip2gQKOEhk/mx14o7eiFRaU+VNNSP4MOiIkwb/D6B3G1Mfas7C/Fit8EsSV2/IWjZElx/Gs6A4ww==
  dependencies:
    restructure "^3.0.0"

jiti@^1.19.1:
  version "1.21.7"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz"
  integrity sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==

jose@^4.14.4:
  version "4.15.9"
  resolved "https://registry.npmjs.org/jose/-/jose-4.15.9.tgz"
  integrity sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==

js-file-download@^0.4.12:
  version "0.4.12"
  resolved "https://registry.npmjs.org/js-file-download/-/js-file-download-0.4.12.tgz"
  integrity sha512-rML+NkoD08p5Dllpjo0ffy4jRHeY6Zsapvr/W86N7E0yuzAO6qa5X9+xog6zQNlH102J7IXljNY2FtS6Lj3ucg==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
  integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
  dependencies:
    bignumber.js "^9.0.0"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
  integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsondiffpatch@0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/jsondiffpatch/-/jsondiffpatch-0.6.0.tgz"
  integrity sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==
  dependencies:
    "@types/diff-match-patch" "^1.0.36"
    chalk "^5.3.0"
    diff-match-patch "^1.0.5"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jspdf-autotable@^3.8.4:
  version "3.8.4"
  resolved "https://registry.npmjs.org/jspdf-autotable/-/jspdf-autotable-3.8.4.tgz"
  integrity sha512-rSffGoBsJYX83iTRv8Ft7FhqfgEL2nLpGAIiqruEQQ3e4r0qdLFbPUB7N9HAle0I3XgpisvyW751VHCqKUVOgQ==

jspdf@^2.3.1, jspdf@^2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/jspdf/-/jspdf-2.5.2.tgz"
  integrity sha512-myeX9c+p7znDWPk0eTrujCzNjT+CXdXyk7YmJq5nD5V7uLLKmSXnlQ/Jn/kuo3X09Op70Apm0rQSnFWyGK8uEQ==
  dependencies:
    "@babel/runtime" "^7.23.2"
    atob "^2.1.2"
    btoa "^1.2.1"
    fflate "^0.8.1"
  optionalDependencies:
    canvg "^3.0.6"
    core-js "^3.6.0"
    dompurify "^2.5.4"
    html2canvas "^1.0.0-rc.5"

jszip@^3.10.1:
  version "3.10.1"
  resolved "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz"
  integrity sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

jwa@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/jwa/-/jwa-2.0.0.tgz"
  integrity sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz"
  integrity sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==
  dependencies:
    jwa "^2.0.0"
    safe-buffer "^5.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

kleur@^4.1.5:
  version "4.1.5"
  resolved "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz"
  integrity sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==

leac@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/leac/-/leac-0.6.0.tgz"
  integrity sha512-y+SqErxb8h7nE/fiEX07jsbuhrpO9lL8eca7/Y1nuWV2moNlXhyd59iDGcRf6moVyDMbmTNzL40SUyrFU/yDpg==

lib0@^0.2.87, lib0@^0.2.99:
  version "0.2.104"
  resolved "https://registry.npmjs.org/lib0/-/lib0-0.2.104.tgz"
  integrity sha512-1tqKRANSPTcjs/yjPoKh52oRM2u5AYdd8jie8sDiN8/5kpWWiQSHUGgtB4VEXLw1chVL3QPSPp8q9RWqzSn2FA==
  dependencies:
    isomorphic.js "^0.2.4"

lie@~3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  integrity sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==
  dependencies:
    immediate "~3.0.5"

lilconfig@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lilconfig@^3.0.0:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

linebreak@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/linebreak/-/linebreak-1.1.0.tgz"
  integrity sha512-MHp03UImeVhB7XZtjd0E4n6+3xr5Dq/9xI/5FptGk5FrbDR3zagPa2DS6U8ks/3HjbKWG9Q1M2ufOzxV2qLYSQ==
  dependencies:
    base64-js "0.0.8"
    unicode-trie "^2.0.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

linkify-it@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/linkify-it/-/linkify-it-5.0.0.tgz"
  integrity sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==
  dependencies:
    uc.micro "^2.0.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==

lodash@^4.17.15, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-5.1.0.tgz"
  integrity sha512-l0x2DvrW294C9uDCoQe1VSU4gf529FkSZ6leBl4TiqZH/e+0R7hSfHQBNut2mNygDgHwvYHfFLn6Oxb3VWj2rA==
  dependencies:
    chalk "^5.0.0"
    is-unicode-supported "^1.1.0"

long@*, long@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npmjs.org/long/-/long-5.3.1.tgz"
  integrity sha512-ka87Jz3gcx/I7Hal94xaN2tZEOPoUOEVftkQqZx2EeQRN7LGdfLlI3FvZ+7WDplm+vK2Urx9ULrvSowtdCieng==

longest-streak@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz"
  integrity sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowlight@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/lowlight/-/lowlight-3.3.0.tgz"
  integrity sha512-0JNhgFoPvP6U6lE/UdVsSq99tn6DhjjpAj5MxG49ewd2mOBVtwWYIT8ClyABhq198aXXODMU6Ox8DrGy/CpTZQ==
  dependencies:
    "@types/hast" "^3.0.0"
    devlop "^1.0.0"
    highlight.js "~11.11.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.436.0:
  version "0.436.0"
  resolved "https://registry.npmjs.org/lucide-react/-/lucide-react-0.436.0.tgz"
  integrity sha512-N292bIxoqm1aObAg0MzFtvhYwgQE6qnIOWx/GLj5ONgcTPH6N0fD9bVq/GfdeC9ZORBXozt/XeEKDpiB3x3vlQ==

markdown-it-task-lists@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/markdown-it-task-lists/-/markdown-it-task-lists-2.1.1.tgz"
  integrity sha512-TxFAc76Jnhb2OUu+n3yz9RMu4CwGfaT788br6HhEDlvWfdeJcLUsxk1Hgw2yJio0OXsxv7pyIPmvECY7bMbluA==

markdown-it@^14.0.0, markdown-it@^14.1.0:
  version "14.1.0"
  resolved "https://registry.npmjs.org/markdown-it/-/markdown-it-14.1.0.tgz"
  integrity sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==
  dependencies:
    argparse "^2.0.1"
    entities "^4.4.0"
    linkify-it "^5.0.0"
    mdurl "^2.0.0"
    punycode.js "^2.3.1"
    uc.micro "^2.1.0"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

mdast-util-from-markdown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz"
  integrity sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-mdx-expression@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz"
  integrity sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-jsx@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz"
  integrity sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    ccount "^2.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    parse-entities "^4.0.0"
    stringify-entities "^4.0.0"
    unist-util-stringify-position "^4.0.0"
    vfile-message "^4.0.0"

mdast-util-mdxjs-esm@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz"
  integrity sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz"
  integrity sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-hast@^13.0.0:
  version "13.2.0"
  resolved "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz"
  integrity sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    devlop "^1.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    trim-lines "^3.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz"
  integrity sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz"
  integrity sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==
  dependencies:
    "@types/mdast" "^4.0.0"

mdurl@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mdurl/-/mdurl-2.0.0.tgz"
  integrity sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==

media-engine@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/media-engine/-/media-engine-1.0.3.tgz"
  integrity sha512-aa5tG6sDoK+k70B9iEX1NeyfT8ObCKhNDs6lJVpwF6r8vhUfuKMslIcirq6HIUYuuUYLefcEQOn9bSBOvawtwg==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromark-core-commonmark@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz"
  integrity sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz"
  integrity sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz"
  integrity sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz"
  integrity sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz"
  integrity sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz"
  integrity sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz"
  integrity sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz"
  integrity sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz"
  integrity sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz"
  integrity sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz"
  integrity sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz"
  integrity sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz"
  integrity sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==

micromark-util-html-tag-name@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz"
  integrity sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz"
  integrity sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz"
  integrity sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz"
  integrity sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz"
  integrity sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz"
  integrity sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==

micromark-util-types@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz"
  integrity sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==

micromark@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz"
  integrity sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@^2.1.35:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz"
  integrity sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==

minimatch@^7.4.3:
  version "7.4.6"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-7.4.6.tgz"
  integrity sha512-sBz8G/YjVniEz6lKPNpKxXwazJe4c19fEfV2GDMX6AjFz+MX9uDWIZW8XreVhkFW3fkIdTv/gxWr/Kks5FFAVw==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.4, minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz"
  integrity sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==
  dependencies:
    minipass "^7.1.2"

mkdirp@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-2.1.6.tgz"
  integrity sha512-+hEnITedc8LAtIP9u3HJDFIdcLV2vXP33sqLLIzkv1Db1zO/1OxbvYf0Y1OC/S/Qo5dxHXepofhmxL02PsKe+A==

mkdirp@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz"
  integrity sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==

motion-dom@^11.18.1:
  version "11.18.1"
  resolved "https://registry.npmjs.org/motion-dom/-/motion-dom-11.18.1.tgz"
  integrity sha512-g76KvA001z+atjfxczdRtw/RXOM3OMSdd1f4DL77qCTF/+avrRJiawSG4yDibEQ215sr9kpinSlX2pCTJ9zbhw==
  dependencies:
    motion-utils "^11.18.1"

motion-utils@^11.18.1:
  version "11.18.1"
  resolved "https://registry.npmjs.org/motion-utils/-/motion-utils-11.18.1.tgz"
  integrity sha512-49Kt+HKjtbJKLtgO/LKj9Ld+6vw9BjH5d9sc40R/kVyH8GLAXgT42M2NnuPcJNuA3s9ZfZBUcwIgpmZWGEE+hA==

ms@^2.0.0, ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

msw@^2.7.1:
  version "2.7.4"
  resolved "https://registry.npmjs.org/msw/-/msw-2.7.4.tgz"
  integrity sha512-A2kuMopOjAjNEYkn0AnB1uj+x7oBjLIunFk7Ud4icEnVWFf6iBekn8oXW4zIwcpfEdWP9sLqyVaHVzneWoGEww==
  dependencies:
    "@bundled-es-modules/cookie" "^2.0.1"
    "@bundled-es-modules/statuses" "^1.0.1"
    "@bundled-es-modules/tough-cookie" "^0.1.6"
    "@inquirer/confirm" "^5.0.0"
    "@mswjs/interceptors" "^0.37.0"
    "@open-draft/deferred-promise" "^2.2.0"
    "@open-draft/until" "^2.1.0"
    "@types/cookie" "^0.6.0"
    "@types/statuses" "^2.0.4"
    graphql "^16.8.1"
    headers-polyfill "^4.0.2"
    is-node-process "^1.2.0"
    outvariant "^1.4.3"
    path-to-regexp "^6.3.0"
    picocolors "^1.1.1"
    strict-event-emitter "^0.5.1"
    type-fest "^4.26.1"
    yargs "^17.7.2"

mute-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-2.0.0.tgz"
  integrity sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA==

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.6, nanoid@^3.3.7, nanoid@^3.3.8:
  version "3.3.11"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

next-superjson-plugin@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/next-superjson-plugin/-/next-superjson-plugin-0.6.3.tgz"
  integrity sha512-gipGROzbbn1Koq84AZQodIvBdORp9dytIDv07SguwXdxnJb6v05KCmHVNU9L6AWqxjP14qNIWCNdKRDhnGRZrg==
  dependencies:
    hoist-non-react-statics "^3.3.2"

next-themes@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/next-themes/-/next-themes-0.3.0.tgz"
  integrity sha512-/QHIrsYpd6Kfk7xakK4svpDI5mmXP0gfvCoJdGpZQ2TOrQZmsW0QxjaiLn8wbIKjtm4BTSqLoix4lxYYOnLJ/w==

next@14.2.16:
  version "14.2.16"
  resolved "https://registry.npmjs.org/next/-/next-14.2.16.tgz"
  integrity sha512-LcO7WnFu6lYSvCzZoo1dB+IO0xXz5uEv52HF1IUN0IqVTUIZGHuuR10I5efiLadGt+4oZqTcNZyVVEem/TM5nA==
  dependencies:
    "@next/env" "14.2.16"
    "@swc/helpers" "0.5.5"
    busboy "1.6.0"
    caniuse-lite "^1.0.30001579"
    graceful-fs "^4.2.11"
    postcss "8.4.31"
    styled-jsx "5.1.1"
  optionalDependencies:
    "@next/swc-darwin-arm64" "14.2.16"
    "@next/swc-darwin-x64" "14.2.16"
    "@next/swc-linux-arm64-gnu" "14.2.16"
    "@next/swc-linux-arm64-musl" "14.2.16"
    "@next/swc-linux-x64-gnu" "14.2.16"
    "@next/swc-linux-x64-musl" "14.2.16"
    "@next/swc-win32-arm64-msvc" "14.2.16"
    "@next/swc-win32-ia32-msvc" "14.2.16"
    "@next/swc-win32-x64-msvc" "14.2.16"

node-domexception@1.0.0, node-domexception@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz"
  integrity sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-fetch@^3.3.0, node-fetch@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz"
  integrity sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==
  dependencies:
    data-uri-to-buffer "^4.0.0"
    fetch-blob "^3.1.4"
    formdata-polyfill "^4.0.10"

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

normalize-svg-path@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/normalize-svg-path/-/normalize-svg-path-1.1.0.tgz"
  integrity sha512-r9KHKG2UUeB5LoTouwDzBy2VxXlHsiM6fyLQvnJa0S5hrhzqElH/CH7TUGhT1fVvIYBIKf3OpY4YJ4CK+iaqHg==
  dependencies:
    svg-arc-to-cubic-bezier "^3.0.0"

npm-normalize-package-bin@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz"
  integrity sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz"
  integrity sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==
  dependencies:
    path-key "^4.0.0"

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz"
  integrity sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
  dependencies:
    mimic-fn "^4.0.0"

openai@^4.91.1:
  version "4.94.0"
  resolved "https://registry.npmjs.org/openai/-/openai-4.94.0.tgz"
  integrity sha512-WVmr9HWcwfouLJ7R3UHd2A93ClezTPuJljQxkCYQAL15Sjyt+FBNoqEz5MHSdH/ebQrVyvRhFyn/bvdqtSPyIA==
  dependencies:
    "@types/node" "^18.11.18"
    "@types/node-fetch" "^2.6.4"
    abort-controller "^3.0.0"
    agentkeepalive "^4.2.1"
    form-data-encoder "1.7.2"
    formdata-node "^4.3.2"
    node-fetch "^2.6.7"

ora@^6.1.2:
  version "6.3.1"
  resolved "https://registry.npmjs.org/ora/-/ora-6.3.1.tgz"
  integrity sha512-ERAyNnZOfqM+Ao3RAvIXkYh5joP220yf59gVe2X/cI6SiCxIdi4c9HZKZD8R6q/RDXEje1THBju6iExiSsgJaQ==
  dependencies:
    chalk "^5.0.0"
    cli-cursor "^4.0.0"
    cli-spinners "^2.6.1"
    is-interactive "^2.0.0"
    is-unicode-supported "^1.1.0"
    log-symbols "^5.1.0"
    stdin-discarder "^0.1.0"
    strip-ansi "^7.0.1"
    wcwidth "^1.0.1"

orderedmap@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/orderedmap/-/orderedmap-2.1.1.tgz"
  integrity sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g==

outvariant@^1.4.0, outvariant@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/outvariant/-/outvariant-1.4.3.tgz"
  integrity sha512-+Sl2UErvtsoajRDKCE5/dBz4DIvHXQQnAxtQTF04OJxY0+DyZXSo5P5Bb7XYWOh81syohlYL24hbDwxedPUJCA==

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

pako@^0.2.5:
  version "0.2.9"
  resolved "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz"
  integrity sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==

pako@~1.0.2, pako@~1.0.5:
  version "1.0.11"
  resolved "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-entities@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz"
  integrity sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==
  dependencies:
    "@types/unist" "^2.0.0"
    character-entities-legacy "^3.0.0"
    character-reference-invalid "^2.0.0"
    decode-named-character-reference "^1.0.0"
    is-alphanumerical "^2.0.0"
    is-decimal "^2.0.0"
    is-hexadecimal "^2.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-svg-path@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ==

parseley@^0.12.0:
  version "0.12.1"
  resolved "https://registry.npmjs.org/parseley/-/parseley-0.12.1.tgz"
  integrity sha512-e6qHKe3a9HWr0oMRVDTRhKce+bRO8VGQR3NyVwcjwrbhMmFCX9KszEV35+rn4AdilFAq9VPxP/Fe1wC9Qjd2lw==
  dependencies:
    leac "^0.6.0"
    peberminta "^0.9.0"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-browserify/-/path-browserify-1.0.1.tgz"
  integrity sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz"
  integrity sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz"
  integrity sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

peberminta@^0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/peberminta/-/peberminta-0.9.0.tgz"
  integrity sha512-XIxfHpEuSJbITd1H3EeQwpcZbTLHc+VVr8ANI9t5sit565tsI4/xK3KWTUFE2e6QiangUkh3B0jihzmGnNrRsQ==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pirates@^4.0.1:
  version "4.0.7"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz"
  integrity sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.0.1, postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.0.11, postcss-selector-parser@^6.1.1:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@8.4.31:
  version "8.4.31"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz"
  integrity sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@8.4.33, postcss@^8.4.23, postcss@^8.4.24:
  version "8.4.33"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.33.tgz"
  integrity sha512-Kkpbhhdjw2qQs2O2DGX+8m5OVqEcbB9HRBvuYM9pgrjEFUg30A9LmXNlTAUj4S9kgtGyrMbTzVjH7E+s5Re2yg==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postgres@^3.4.5:
  version "3.4.5"
  resolved "https://registry.npmjs.org/postgres/-/postgres-3.4.5.tgz"
  integrity sha512-cDWgoah1Gez9rN3H4165peY9qfpEo+SA61oQv65O3cRUE1pOEoJWwddwcqKE8XZYjbblOJlYDlLV4h67HrEVDg==

prettier@3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz"
  integrity sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw==

proc-log@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/proc-log/-/proc-log-5.0.0.tgz"
  integrity sha512-Azwzvl90HaF0aCz1JrDdXQykFakSSNPaPoiZ9fm5qJIMHioDZEi7OAdRwSm6rSoPtY3Qutnm3L7ogmg3dc+wbQ==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@15.x, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-information@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/property-information/-/property-information-7.0.0.tgz"
  integrity sha512-7D/qOz/+Y4X/rzSB6jKxKUsQnphO046ei8qxG59mtM3RG3DHgTK81HrxrmoDVINJb8NKT5ZsRbwHvQ6B68Iyhg==

prosemirror-changeset@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/prosemirror-changeset/-/prosemirror-changeset-2.2.1.tgz"
  integrity sha512-J7msc6wbxB4ekDFj+n9gTW/jav/p53kdlivvuppHsrZXCaQdVgRghoZbSS3kwrRyAstRVQ4/+u5k7YfLgkkQvQ==
  dependencies:
    prosemirror-transform "^1.0.0"

prosemirror-collab@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/prosemirror-collab/-/prosemirror-collab-1.3.1.tgz"
  integrity sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==
  dependencies:
    prosemirror-state "^1.0.0"

prosemirror-commands@^1.0.0, prosemirror-commands@^1.0.7, prosemirror-commands@^1.6.2:
  version "1.7.1"
  resolved "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.7.1.tgz"
  integrity sha512-rT7qZnQtx5c0/y/KlYaGvtG411S97UaL6gdp6RIZ23DLHanMYLyfGBV5DtSnZdthQql7W+lEVbpSfwtO8T+L2w==
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.10.2"

prosemirror-dropcursor@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/prosemirror-dropcursor/-/prosemirror-dropcursor-1.8.1.tgz"
  integrity sha512-M30WJdJZLyXHi3N8vxN6Zh5O8ZBbQCz0gURTfPmTIBNQ5pxrdU7A58QkNqfa98YEjSAL1HUyyU34f6Pm5xBSGw==
  dependencies:
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.1.0"
    prosemirror-view "^1.1.0"

prosemirror-gapcursor@^1.0.2, prosemirror-gapcursor@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/prosemirror-gapcursor/-/prosemirror-gapcursor-1.3.2.tgz"
  integrity sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==
  dependencies:
    prosemirror-keymap "^1.0.0"
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-view "^1.0.0"

prosemirror-history@^1.0.0, prosemirror-history@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz"
  integrity sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==
  dependencies:
    prosemirror-state "^1.2.2"
    prosemirror-transform "^1.0.0"
    prosemirror-view "^1.31.0"
    rope-sequence "^1.3.0"

prosemirror-inputrules@^1.0.1, prosemirror-inputrules@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/prosemirror-inputrules/-/prosemirror-inputrules-1.5.0.tgz"
  integrity sha512-K0xJRCmt+uSw7xesnHmcn72yBGTbY45vm8gXI4LZXbx2Z0jwh5aF9xrGQgrVPu0WbyFVFF3E/o9VhJYz6SQWnA==
  dependencies:
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.0.0"

prosemirror-keymap@^1.0.0, prosemirror-keymap@^1.0.1, prosemirror-keymap@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.2.tgz"
  integrity sha512-EAlXoksqC6Vbocqc0GtzCruZEzYgrn+iiGnNjsJsH4mrnIGex4qbLdWWNza3AW5W36ZRrlBID0eM6bdKH4OStQ==
  dependencies:
    prosemirror-state "^1.0.0"
    w3c-keyname "^2.2.0"

prosemirror-markdown@^1.11.1, prosemirror-markdown@^1.13.1:
  version "1.13.2"
  resolved "https://registry.npmjs.org/prosemirror-markdown/-/prosemirror-markdown-1.13.2.tgz"
  integrity sha512-FPD9rHPdA9fqzNmIIDhhnYQ6WgNoSWX9StUZ8LEKapaXU9i6XgykaHKhp6XMyXlOWetmaFgGDS/nu/w9/vUc5g==
  dependencies:
    "@types/markdown-it" "^14.0.0"
    markdown-it "^14.0.0"
    prosemirror-model "^1.25.0"

prosemirror-menu@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/prosemirror-menu/-/prosemirror-menu-1.2.4.tgz"
  integrity sha512-S/bXlc0ODQup6aiBbWVsX/eM+xJgCTAfMq/nLqaO5ID/am4wS0tTCIkzwytmao7ypEtjj39i7YbJjAgO20mIqA==
  dependencies:
    crelt "^1.0.0"
    prosemirror-commands "^1.0.0"
    prosemirror-history "^1.0.0"
    prosemirror-state "^1.0.0"

prosemirror-model@^1.0.0, prosemirror-model@^1.20.0, prosemirror-model@^1.21.0, prosemirror-model@^1.23.0, prosemirror-model@^1.25.0, prosemirror-model@^1.5.0:
  version "1.25.0"
  resolved "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.0.tgz"
  integrity sha512-/8XUmxWf0pkj2BmtqZHYJipTBMHIdVjuvFzMvEoxrtyGNmfvdhBiRwYt/eFwy2wA9DtBW3RLqvZnjurEkHaFCw==
  dependencies:
    orderedmap "^2.0.0"

prosemirror-schema-basic@^1.2.3:
  version "1.2.4"
  resolved "https://registry.npmjs.org/prosemirror-schema-basic/-/prosemirror-schema-basic-1.2.4.tgz"
  integrity sha512-ELxP4TlX3yr2v5rM7Sb70SqStq5NvI15c0j9j/gjsrO5vaw+fnnpovCLEGIcpeGfifkuqJwl4fon6b+KdrODYQ==
  dependencies:
    prosemirror-model "^1.25.0"

prosemirror-schema-list@^1.0.1, prosemirror-schema-list@^1.4.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/prosemirror-schema-list/-/prosemirror-schema-list-1.5.1.tgz"
  integrity sha512-927lFx/uwyQaGwJxLWCZRkjXG0p48KpMj6ueoYiu4JX05GGuGcgzAy62dfiV8eFZftgyBUvLx76RsMe20fJl+Q==
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.7.3"

prosemirror-state@^1.0.0, prosemirror-state@^1.2.1, prosemirror-state@^1.2.2, prosemirror-state@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz"
  integrity sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==
  dependencies:
    prosemirror-model "^1.0.0"
    prosemirror-transform "^1.0.0"
    prosemirror-view "^1.27.0"

prosemirror-tables@^0.7.7:
  version "0.7.11"
  resolved "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-0.7.11.tgz"
  integrity sha512-IOvz4sE4RgEeI8aKbis6ADqXEZlqnU1aZ/hdZLj6F74HeCGv5cC/hnOkhlip+4IaQvqn1u3oyBWKMzjGlUJuww==
  dependencies:
    prosemirror-keymap "^1.0.0"
    prosemirror-model "^1.0.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.0.0"
    prosemirror-view "^1.0.0"

prosemirror-tables@^1.6.4:
  version "1.7.0"
  resolved "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-1.7.0.tgz"
  integrity sha512-dc9+u4aqT+biw3J/v7p5LyH8uqqXSAjdszfLtrCnDbpMr1F+Gsjtkdiij/1p8qM1gBOCfQeiahhk2pOO9Aa8xA==
  dependencies:
    prosemirror-keymap "^1.2.2"
    prosemirror-model "^1.25.0"
    prosemirror-state "^1.4.3"
    prosemirror-transform "^1.10.3"
    prosemirror-view "^1.39.1"

prosemirror-trailing-node@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/prosemirror-trailing-node/-/prosemirror-trailing-node-3.0.0.tgz"
  integrity sha512-xiun5/3q0w5eRnGYfNlW1uU9W6x5MoFKWwq/0TIRgt09lv7Hcser2QYV8t4muXbEr+Fwo0geYn79Xs4GKywrRQ==
  dependencies:
    "@remirror/core-constants" "3.0.0"
    escape-string-regexp "^4.0.0"

prosemirror-transform@^1.0.0, prosemirror-transform@^1.1.0, prosemirror-transform@^1.10.2, prosemirror-transform@^1.10.3, prosemirror-transform@^1.7.3:
  version "1.10.3"
  resolved "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.3.tgz"
  integrity sha512-Nhh/+1kZGRINbEHmVu39oynhcap4hWTs/BlU7NnxWj3+l0qi8I1mu67v6mMdEe/ltD8hHvU4FV6PHiCw2VSpMw==
  dependencies:
    prosemirror-model "^1.21.0"

prosemirror-utils@^0.6.6:
  version "0.6.7"
  resolved "https://registry.npmjs.org/prosemirror-utils/-/prosemirror-utils-0.6.7.tgz"
  integrity sha512-nOKcfqyfZBhsephi9HCvEcF6qof7NcbKthnWhXAHJvca5YTaKh6h5mjGSRndm91CnbvRrwlG/BCC0BTu790fDA==

prosemirror-view@^1.0.0, prosemirror-view@^1.1.0, prosemirror-view@^1.27.0, prosemirror-view@^1.31.0, prosemirror-view@^1.37.0, prosemirror-view@^1.39.1, prosemirror-view@^1.6.0:
  version "1.39.1"
  resolved "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.39.1.tgz"
  integrity sha512-GhLxH1xwnqa5VjhJ29LfcQITNDp+f1jzmMPXQfGW9oNrF0lfjPzKvV5y/bjIQkyKpwCX3Fp+GA4dBpMMk8g+ZQ==
  dependencies:
    prosemirror-model "^1.20.0"
    prosemirror-state "^1.0.0"
    prosemirror-transform "^1.1.0"

proto3-json-serializer@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/proto3-json-serializer/-/proto3-json-serializer-3.0.0.tgz"
  integrity sha512-mHPIc7zaJc26HMpgX5J7vXjliYv4Rnn5ICUyINudz76iY4zFMQHTaQXrTFn0EoHnRsLD6BE+OuHhQHFUU93I9A==
  dependencies:
    protobufjs "^7.4.0"

protobufjs@^7.2.5, protobufjs@^7.4.0:
  version "7.5.0"
  resolved "https://registry.npmjs.org/protobufjs/-/protobufjs-7.5.0.tgz"
  integrity sha512-Z2E/kOY1QjoMlCytmexzYfDm/w5fKAiRwpSzGtdnXW1zC88Z2yXazHHrOtwCzn+7wSxyE8PYM4rvVcMphF9sOA==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

psl@^1.1.33:
  version "1.15.0"
  resolved "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz"
  integrity sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==
  dependencies:
    punycode "^2.3.1"

pump@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz"
  integrity sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/pumpify/-/pumpify-2.0.1.tgz"
  integrity sha512-m7KOje7jZxrmutanlkS1daj1dS6z6BgslzOXmcSEpIlCxM3VJH7lG5QLeck/6hgF6F4crFf01UtQmNsJfweTAw==
  dependencies:
    duplexify "^4.1.1"
    inherits "^2.0.3"
    pump "^3.0.0"

punycode.js@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode.js/-/punycode.js-2.3.1.tgz"
  integrity sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==

punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

querystringify@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  integrity sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

queue@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz"
  integrity sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==
  dependencies:
    inherits "~2.0.3"

raf@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

re-resizable@6.11.2:
  version "6.11.2"
  resolved "https://registry.npmjs.org/re-resizable/-/re-resizable-6.11.2.tgz"
  integrity sha512-2xI2P3OHs5qw7K0Ud1aLILK6MQxW50TcO+DetD9eIV58j84TqYeHoZcL9H4GXFXXIh7afhH8mv5iUCXII7OW7A==

react-apexcharts@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/react-apexcharts/-/react-apexcharts-1.7.0.tgz"
  integrity sha512-03oScKJyNLRf0Oe+ihJxFZliBQM9vW3UWwomVn4YVRTN1jsIR58dLWt0v1sb8RwJVHDMbeHiKQueM0KGpn7nOA==
  dependencies:
    prop-types "^15.8.1"

react-colorful@^5.6.1:
  version "5.6.1"
  resolved "https://registry.npmjs.org/react-colorful/-/react-colorful-5.6.1.tgz"
  integrity sha512-1exovf0uGTGyq5mXQT0zgQ80uvj2PCwvF8zY1RN9/vbJVSjSo3fsB/4L3ObbF7u70NduSiK4xu4Y6q1MHoUGEw==

react-component-export-image@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/react-component-export-image/-/react-component-export-image-1.0.6.tgz"
  integrity sha512-LQHVt6HPHyyu96J57D5tSsYD1E5Hpky6X4HKbnCgepIK/aAnyQslNmpvKBAYPehCb6baGF4bWl7Ug15nGb2bpw==
  dependencies:
    html2canvas "^1.0.0-rc.7"
    jspdf "^2.3.1"

react-day-picker@8.10.1:
  version "8.10.1"
  resolved "https://registry.npmjs.org/react-day-picker/-/react-day-picker-8.10.1.tgz"
  integrity sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==

react-dnd-html5-backend@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz"
  integrity sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==
  dependencies:
    dnd-core "^16.0.1"

react-dnd@^16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/react-dnd/-/react-dnd-16.0.1.tgz"
  integrity sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==
  dependencies:
    "@react-dnd/invariant" "^4.0.1"
    "@react-dnd/shallowequal" "^4.0.1"
    dnd-core "^16.0.1"
    fast-deep-equal "^3.1.3"
    hoist-non-react-statics "^3.3.2"

react-dom@^18.2.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-draggable@4.4.6, react-draggable@^4.0.3, react-draggable@^4.4.5:
  version "4.4.6"
  resolved "https://registry.npmjs.org/react-draggable/-/react-draggable-4.4.6.tgz"
  integrity sha512-LtY5Xw1zTPqHkVmtM3X8MUOxNDOUhv/khTgBgrUvwaS064bwVvxT+q5El0uUFNx5IEPKXuRejr7UqLwBIg5pdw==
  dependencies:
    clsx "^1.1.1"
    prop-types "^15.8.1"

react-dropzone@^14.3.5:
  version "14.3.8"
  resolved "https://registry.npmjs.org/react-dropzone/-/react-dropzone-14.3.8.tgz"
  integrity sha512-sBgODnq+lcA4P296DY4wacOZz3JFpD99fp+hb//iBO2HHnyeZU3FwWyXJ6salNpqQdsZrgMrotuko/BdJMV8Ug==
  dependencies:
    attr-accept "^2.2.4"
    file-selector "^2.1.0"
    prop-types "^15.8.1"

react-grid-layout@^1.4.4:
  version "1.5.1"
  resolved "https://registry.npmjs.org/react-grid-layout/-/react-grid-layout-1.5.1.tgz"
  integrity sha512-4Fr+kKMk0+m1HL/BWfHxi/lRuaOmDNNKQDcu7m12+NEYcen20wIuZFo789u3qWCyvUsNUxCiyf0eKq4WiJSNYw==
  dependencies:
    clsx "^2.0.0"
    fast-equals "^4.0.3"
    prop-types "^15.8.1"
    react-draggable "^4.4.5"
    react-resizable "^3.0.5"
    resize-observer-polyfill "^1.5.1"

react-hook-form@^7.54.1:
  version "7.55.0"
  resolved "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.55.0.tgz"
  integrity sha512-XRnjsH3GVMQz1moZTW53MxfoWN7aDpUg/GpVNc4A3eXRVNdGXfbzJ4vM4aLQ8g6XCUh1nIbx70aaNCl7kxnjog==

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^18.3.1:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-markdown@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/react-markdown/-/react-markdown-10.1.0.tgz"
  integrity sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    hast-util-to-jsx-runtime "^2.0.0"
    html-url-attributes "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    remark-parse "^11.0.0"
    remark-rehype "^11.0.0"
    unified "^11.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

react-promise-suspense@0.3.4:
  version "0.3.4"
  resolved "https://registry.npmjs.org/react-promise-suspense/-/react-promise-suspense-0.3.4.tgz"
  integrity sha512-I42jl7L3Ze6kZaq+7zXWSunBa3b1on5yfvUW6Eo/3fFOj6dZ5Bqmcd264nJbTK/gn1HjjILAjSwnZbV4RpSaNQ==
  dependencies:
    fast-deep-equal "^2.0.1"

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  integrity sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.6.3"
  resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.3.tgz"
  integrity sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-resizable@^3.0.5:
  version "3.0.5"
  resolved "https://registry.npmjs.org/react-resizable/-/react-resizable-3.0.5.tgz"
  integrity sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w==
  dependencies:
    prop-types "15.x"
    react-draggable "^4.0.3"

react-rnd@^10.4.13:
  version "10.5.2"
  resolved "https://registry.npmjs.org/react-rnd/-/react-rnd-10.5.2.tgz"
  integrity sha512-0Tm4x7k7pfHf2snewJA8x7Nwgt3LV+58MVEWOVsFjk51eYruFEa6Wy7BNdxt4/lH0wIRsu7Gm3KjSXY2w7YaNw==
  dependencies:
    re-resizable "6.11.2"
    react-draggable "4.4.6"
    tslib "2.6.2"

react-signature-canvas@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/react-signature-canvas/-/react-signature-canvas-1.0.7.tgz"
  integrity sha512-yo0x0uTMVmcClaqryuQu6F8xEVapk5zdM9/nuQ7GalDJWccoVNZPMmCFGK7U5r3I0mrEHPB9E5/rFSYUgZcfmA==
  dependencies:
    signature_pad "^2.3.2"
    trim-canvas "^0.1.0"

react-smooth@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.4.tgz"
  integrity sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==
  dependencies:
    fast-equals "^5.0.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  integrity sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-to-print@^3.0.2:
  version "3.0.6"
  resolved "https://registry.npmjs.org/react-to-print/-/react-to-print-3.0.6.tgz"
  integrity sha512-K/jFxkUifbfVnu1XyinM6AB6zAq0VMw0lH/6WJpkdlChoqqvEOE/BGOxYN2xOmu8f72isTTU5DNatK/j0Lfc+Q==

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@^18.2.0:
  version "18.3.1"
  resolved "https://registry.npmjs.org/react/-/react-18.3.1.tgz"
  integrity sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==
  dependencies:
    loose-envify "^1.1.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

read-cmd-shim@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz"
  integrity sha512-SEbJV7tohp3DAAILbEMPXavBjAnMN0tVnh4+9G8ihV4Pq3HYF9h8QNez9zkJ1ILkv9G2BjdzwctznGZXgu/HGw==

readable-stream@^3.1.1, readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recast@^0.23.2:
  version "0.23.11"
  resolved "https://registry.npmjs.org/recast/-/recast-0.23.11.tgz"
  integrity sha512-YTUo+Flmw4ZXiWfQKGcwwc11KnoRAYgzAE2E7mXKCjSviTKShtxBsN6YUUBB2gtaBzKzeKunxhUwNHQuRryhWA==
  dependencies:
    ast-types "^0.16.1"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tiny-invariant "^1.3.3"
    tslib "^2.0.1"

recharts-scale@^0.4.4:
  version "0.4.5"
  resolved "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz"
  integrity sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==
  dependencies:
    decimal.js-light "^2.4.1"

recharts-to-png@^2.3.2:
  version "2.4.0"
  resolved "https://registry.npmjs.org/recharts-to-png/-/recharts-to-png-2.4.0.tgz"
  integrity sha512-2enABoIsBrI+8NrdFzQCuS/6pIUQVDy9KOPP8VeKN0szLNix5nzfwtyopdTDo6fSKtjojbsQpQ6AN/s5bfJkWg==
  dependencies:
    html2canvas "^1.2.0"

recharts@^2.15.3:
  version "2.15.3"
  resolved "https://registry.npmjs.org/recharts/-/recharts-2.15.3.tgz"
  integrity sha512-EdOPzTwcFSuqtvkDoaM5ws/Km1+WTAO2eizL7rqiG0V2UVhTnz0m7J2i0CjVPUCdEkZImaWvXLbZDS2H5t6GFQ==
  dependencies:
    clsx "^2.0.0"
    eventemitter3 "^4.0.1"
    lodash "^4.17.21"
    react-is "^18.3.1"
    react-smooth "^4.0.4"
    recharts-scale "^0.4.4"
    tiny-invariant "^1.3.1"
    victory-vendor "^36.6.8"

redux@^4.2.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz"
  integrity sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==
  dependencies:
    "@babel/runtime" "^7.9.2"

regenerator-runtime@^0.13.7:
  version "0.13.11"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regression@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/regression/-/regression-2.0.1.tgz"
  integrity sha512-A4XYsc37dsBaNOgEjkJKzfJlE394IMmUPlI/p3TTI9u3T+2a+eox5Pr/CPUqF0eszeWZJPAc6QkroAhuUpWDJQ==

remark-parse@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz"
  integrity sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    micromark-util-types "^2.0.0"
    unified "^11.0.0"

remark-rehype@^11.0.0:
  version "11.1.2"
  resolved "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz"
  integrity sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    mdast-util-to-hast "^13.0.0"
    unified "^11.0.0"
    vfile "^6.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resend@^4.5.1:
  version "4.5.1"
  resolved "https://registry.npmjs.org/resend/-/resend-4.5.1.tgz"
  integrity sha512-ryhHpZqCBmuVyzM19IO8Egtc2hkWI4JOL5lf5F3P7Dydu3rFeX6lHNpGqG0tjWoZ63rw0l731JEmuJZBdDm3og==
  dependencies:
    "@react-email/render" "1.0.6"

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.7, resolve@^1.22.2:
  version "1.22.10"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-4.0.0.tgz"
  integrity sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

restructure@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/restructure/-/restructure-3.0.2.tgz"
  integrity sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==

retry-request@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/retry-request/-/retry-request-8.0.0.tgz"
  integrity sha512-dJkZNmyV9C8WKUmbdj1xcvVlXBSvsUQCkg89TCK8rD72RdSn9A2jlXlS2VuYSTHoPJjJEfUHhjNYrlvuksF9cg==
  dependencies:
    "@types/request" "^2.48.12"
    extend "^3.0.2"
    teeny-request "^10.0.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz"
  integrity sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==

rgbcolor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  integrity sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw==

rope-sequence@^1.3.0:
  version "1.3.4"
  resolved "https://registry.npmjs.org/rope-sequence/-/rope-sequence-1.3.4.tgz"
  integrity sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.0.1, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

save-svg-as-png@^1.4.17:
  version "1.4.17"
  resolved "https://registry.npmjs.org/save-svg-as-png/-/save-svg-as-png-1.4.17.tgz"
  integrity sha512-7QDaqJsVhdFPwviCxkgHiGm9omeaMBe1VKbHySWU6oFB2LtnGCcYS13eVoslUgq6VZC6Tjq/HddBd1K6p2PGpA==

scheduler@0.25.0-rc-603e6108-20241029:
  version "0.25.0-rc-603e6108-20241029"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.25.0-rc-603e6108-20241029.tgz"
  integrity sha512-pFwF6H1XrSdYYNLfOcGlM28/j8CGLu8IvdrxqhjWULe2bPcKiKW4CV+OWqR/9fT52mywx65l7ysNkjLKBda7eA==

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==
  dependencies:
    loose-envify "^1.1.0"

secure-json-parse@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/secure-json-parse/-/secure-json-parse-2.7.0.tgz"
  integrity sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==

selderee@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmjs.org/selderee/-/selderee-0.11.0.tgz"
  integrity sha512-5TF+l7p4+OsnP8BCCvSyZiSPc4x4//p5uPwK8TCnVPJYRmU2aYKMpOXvw8zM5a5JvuuCGN1jmsMwuU2W02ukfA==
  dependencies:
    parseley "^0.12.0"

semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  integrity sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  integrity sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==

shadcn@^2.1.6:
  version "2.4.1"
  resolved "https://registry.npmjs.org/shadcn/-/shadcn-2.4.1.tgz"
  integrity sha512-QbOT0vwyYxCTLfvzR4lTIJHlc/e+Sqch3xogaGjCKjvrjd+0hbNZc2vBcFEFH69VI+PN167lvkQahXGd53+XQQ==
  dependencies:
    "@antfu/ni" "^23.2.0"
    "@babel/core" "^7.22.1"
    "@babel/parser" "^7.22.6"
    "@babel/plugin-transform-typescript" "^7.22.5"
    commander "^10.0.0"
    cosmiconfig "^8.1.3"
    deepmerge "^4.3.1"
    diff "^5.1.0"
    execa "^7.0.0"
    fast-glob "^3.3.2"
    fs-extra "^11.1.0"
    https-proxy-agent "^6.2.0"
    kleur "^4.1.5"
    msw "^2.7.1"
    node-fetch "^3.3.0"
    ora "^6.1.2"
    postcss "^8.4.24"
    prompts "^2.4.2"
    recast "^0.23.2"
    stringify-object "^5.0.0"
    ts-morph "^18.0.0"
    tsconfig-paths "^4.2.0"
    zod "^3.20.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

signature_pad@^2.3.2:
  version "2.3.2"
  resolved "https://registry.npmjs.org/signature_pad/-/signature_pad-2.3.2.tgz"
  integrity sha512-peYXLxOsIY6MES2TrRLDiNg2T++8gGbpP2yaC+6Ohtxr+a2dzoaqWosWDY9sWqTAAk6E/TyQO+LJw9zQwyu5kA==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

sonner@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/sonner/-/sonner-1.7.4.tgz"
  integrity sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==

source-map-js@^1.0.2:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

space-separated-tokens@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz"
  integrity sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://registry.npmjs.org/ssf/-/ssf-0.11.2.tgz"
  integrity sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==
  dependencies:
    frac "~1.1.2"

stackblur-canvas@^2.0.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz"
  integrity sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ==

statuses@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

stdin-discarder@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/stdin-discarder/-/stdin-discarder-0.1.0.tgz"
  integrity sha512-xhV7w8S+bUwlPTb4bAOUQhv8/cSS5offJuX8GQGq32ONF0ZtDWKfkdomM3HMRA+LhX6um/FZ0COqlwsjD53LeQ==
  dependencies:
    bl "^5.0.0"

stream-events@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/stream-events/-/stream-events-1.0.5.tgz"
  integrity sha512-E1GUzBSgvct8Jsb3v2X15pjzN1tYebtbLaMg+eBOUOAxgbLoSbT2NS91ckc5lJD1KfLjId+jXJRgo0qnV5Nerg==
  dependencies:
    stubs "^3.0.0"

stream-shift@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.3.tgz"
  integrity sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

strict-event-emitter@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmjs.org/strict-event-emitter/-/strict-event-emitter-0.5.1.tgz"
  integrity sha512-vMgjE/GGEPEFnhFub6pa4FmJBRBVOLpIII2hvCZ8Kzb7K0hlHo7mQv6xYrBvCL2LtAIBwFUK8wvuJgTVSQ5MFQ==

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

stringify-entities@^4.0.0:
  version "4.0.4"
  resolved "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz"
  integrity sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==
  dependencies:
    character-entities-html4 "^2.0.0"
    character-entities-legacy "^3.0.0"

stringify-object@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/stringify-object/-/stringify-object-5.0.0.tgz"
  integrity sha512-zaJYxz2FtcMb4f+g60KsRNFOpVMUyuJgA51Zi5Z1DOTC3S59+OQiVOzE9GZt0x72uBGWKsQIuBKeF9iusmKFsg==
  dependencies:
    get-own-enumerable-keys "^1.0.0"
    is-obj "^3.0.0"
    is-regexp "^3.1.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz"
  integrity sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==

stubs@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/stubs/-/stubs-3.0.0.tgz"
  integrity sha512-PdHt7hHUJKxvTCgbKX9C1V/ftOcjJQgz8BZwNfV5c4B6dcGqlpelTbJ999jBGZ2jYiPAwcX5dP6oBwVlBlUbxw==

style-to-js@^1.0.0:
  version "1.1.16"
  resolved "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.16.tgz"
  integrity sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==
  dependencies:
    style-to-object "1.0.8"

style-to-object@1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.8.tgz"
  integrity sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==
  dependencies:
    inline-style-parser "0.2.4"

styled-jsx@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.1.tgz"
  integrity sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==
  dependencies:
    client-only "0.0.1"

sucrase@^3.32.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supabase@^2.15.8:
  version "2.20.12"
  resolved "https://registry.npmjs.org/supabase/-/supabase-2.20.12.tgz"
  integrity sha512-8UTRRvQdMsfs7jcHPDfv6cFYwrar5OyqidtB8GSdPCaKfEP+0n4TwCVwu7H0tDCbCYs28HDZDzQAZ8Ox+aVIiA==
  dependencies:
    bin-links "^5.0.0"
    https-proxy-agent "^7.0.2"
    node-fetch "^3.3.2"
    tar "7.4.3"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-arc-to-cubic-bezier@^3.0.0, svg-arc-to-cubic-bezier@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/svg-arc-to-cubic-bezier/-/svg-arc-to-cubic-bezier-3.2.0.tgz"
  integrity sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g==

svg-pathdata@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  integrity sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw==

swapy@^0.4.2:
  version "0.4.2"
  resolved "https://registry.npmjs.org/swapy/-/swapy-0.4.2.tgz"
  integrity sha512-vxs9OjXCfaQRXUxUBZb7KAWSAJeWlOYsG8N6lJBseEaQZ3XfxW6uIvJo/ebyxQ3mdFxNHEWhxPSbfrfCvevh6Q==

swr@^2.2.5:
  version "2.3.3"
  resolved "https://registry.npmjs.org/swr/-/swr-2.3.3.tgz"
  integrity sha512-dshNvs3ExOqtZ6kJBaAsabhPdHyeY4P2cKwRCniDVifBMoG/SVI7tfLWqPXriVspf2Rg4tPzXJTnwaihIeFw2A==
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

tailwind-merge@3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.0.2.tgz"
  integrity sha512-l7z+OYZ7mu3DTqrL88RiKrKIqO3NcpEO8V/Od04bNpvk0kiIFndGEoqfuzvj4yuhRkHKjRkII2z+KS2HfPcSxw==

tailwind-merge@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz"
  integrity sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==

tailwind-variants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/tailwind-variants/-/tailwind-variants-1.0.0.tgz"
  integrity sha512-2WSbv4ulEEyuBKomOunut65D8UZwxrHoRfYnxGcQNnHqlSCp2+B7Yz2W+yrNDrxRodOXtGD/1oCcKGNBnUqMqA==
  dependencies:
    tailwind-merge "3.0.2"

tailwindcss-animate@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz"
  integrity sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==

tailwindcss@3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.1.tgz"
  integrity sha512-qAYmXRfk3ENzuPBakNK0SRrUDipP8NQnEY6772uDhflcQz5EhRdD7JNZxyrFHVQNCwULPBn6FNPp9brpO7ctcA==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.5.3"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.0"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.19.1"
    lilconfig "^2.1.0"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.23"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.1"
    postcss-nested "^6.0.1"
    postcss-selector-parser "^6.0.11"
    resolve "^1.22.2"
    sucrase "^3.32.0"

tar@7.4.3:
  version "7.4.3"
  resolved "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz"
  integrity sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==
  dependencies:
    "@isaacs/fs-minipass" "^4.0.0"
    chownr "^3.0.0"
    minipass "^7.1.2"
    minizlib "^3.0.1"
    mkdirp "^3.0.1"
    yallist "^5.0.0"

teeny-request@^10.0.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/teeny-request/-/teeny-request-10.1.0.tgz"
  integrity sha512-3ZnLvgWF29jikg1sAQ1g0o+lr5JX6sVgYvfUJazn7ZjJroDBUTWp44/+cFVX0bULjv4vci+rBD+oGVAkWqhUbw==
  dependencies:
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.0"
    node-fetch "^3.3.2"
    stream-events "^1.0.5"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  integrity sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==
  dependencies:
    utrie "^1.0.2"

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

throttleit@2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/throttleit/-/throttleit-2.1.0.tgz"
  integrity sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw==

tiny-inflate@^1.0.0, tiny-inflate@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz"
  integrity sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==

tiny-invariant@^1.3.1, tiny-invariant@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  integrity sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==

tippy.js@^6.3.7:
  version "6.3.7"
  resolved "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz"
  integrity sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==
  dependencies:
    "@popperjs/core" "^2.9.0"

tiptap-commands@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/tiptap-commands/-/tiptap-commands-0.5.0.tgz"
  integrity sha512-9y9VSUvUyRPeZEuc9kWKt/sFpDV7XQm9ryfFu2jZswsKjolNNGOwKRpmJ0ae/eELsJ53r3zEGn75lQfz4mS3hg==
  dependencies:
    prosemirror-commands "^1.0.7"
    prosemirror-inputrules "^1.0.1"
    prosemirror-schema-list "^1.0.1"
    tiptap-utils "^0.3.0"

tiptap-markdown@^0.8.10:
  version "0.8.10"
  resolved "https://registry.npmjs.org/tiptap-markdown/-/tiptap-markdown-0.8.10.tgz"
  integrity sha512-iDVkR2BjAqkTDtFX0h94yVvE2AihCXlF0Q7RIXSJPRSR5I0PA1TMuAg6FHFpmqTn4tPxJ0by0CK7PUMlnFLGEQ==
  dependencies:
    "@types/markdown-it" "^13.0.7"
    markdown-it "^14.1.0"
    markdown-it-task-lists "^2.1.1"
    prosemirror-markdown "^1.11.1"

tiptap-slash-react@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/tiptap-slash-react/-/tiptap-slash-react-1.1.4.tgz"
  integrity sha512-sS9+Fa6Umw/o0ndOP+YciSuhaxlc2Ou4c1EzZfPCLEB53yJf7ikP5+uhushP1jf38HaU+V7/8iOEdwgAL6RRIA==

tiptap-utils@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/tiptap-utils/-/tiptap-utils-0.3.0.tgz"
  integrity sha512-bAlmCfzTwFK3IPdGwKpNXpwtCBeNZbZhSePPR/dCvgzJlATnEPJ9y3V/QT/DaEMLkTJBdztjqoJfNXat172sEg==
  dependencies:
    prosemirror-tables "^0.7.7"
    prosemirror-utils "^0.6.6"

tiptap@^0.15.0:
  version "0.15.0"
  resolved "https://registry.npmjs.org/tiptap/-/tiptap-0.15.0.tgz"
  integrity sha512-qvUZC1s8AjfCUk/8yUPuyuOjLwjE+NQmxd4MepXSB1DTLVTlmwjU941Myq8DjaVBqOXqbMwpWJEH3Y6aTWDO6Q==
  dependencies:
    prosemirror-commands "^1.0.7"
    prosemirror-gapcursor "^1.0.2"
    prosemirror-inputrules "^1.0.1"
    prosemirror-keymap "^1.0.1"
    prosemirror-model "^1.5.0"
    prosemirror-state "^1.2.1"
    prosemirror-view "^1.6.0"
    tiptap-commands "^0.5.0"
    tiptap-utils "^0.3.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

tough-cookie@^4.1.4:
  version "4.1.4"
  resolved "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz"
  integrity sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

trim-canvas@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/trim-canvas/-/trim-canvas-0.1.2.tgz"
  integrity sha512-nd4Ga3iLFV94mdhW9JFMLpQbHUyCQuhFOD71PEAt1NjtMD5wbZctzhX8c3agHNybMR5zXD1XTGoIEWk995E6pQ==

trim-lines@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/trim-lines/-/trim-lines-3.0.1.tgz"
  integrity sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==

trough@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/trough/-/trough-2.2.0.tgz"
  integrity sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

ts-morph@^18.0.0:
  version "18.0.0"
  resolved "https://registry.npmjs.org/ts-morph/-/ts-morph-18.0.0.tgz"
  integrity sha512-Kg5u0mk19PIIe4islUI/HWRvm9bC1lHejK4S0oh1zaZ77TMZAEmQC0sHQYiu2RgCQFZKXz1fMVi/7nOOeirznA==
  dependencies:
    "@ts-morph/common" "~0.19.0"
    code-block-writer "^12.0.0"

tsconfig-paths@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  integrity sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

tslib@^2.0.0, tslib@^2.0.1, tslib@^2.1.0, tslib@^2.4.0, tslib@^2.7.0, tslib@^2.8.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-fest@^4.26.1:
  version "4.40.0"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-4.40.0.tgz"
  integrity sha512-ABHZ2/tS2JkvH1PEjxFDTUWC8dB5OsIGZP4IFLhR293GqT5Y5qB1WwL2kMPYhQW9DVgVD8Hd7I8gjwPIf5GFkw==

typescript@5.3.3:
  version "5.3.3"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.3.3.tgz"
  integrity sha512-pXWcraxM0uxAS+tN0AG/BF2TyqmHO014Z070UsJ+pFvYuRSq8KH8DmWpnbXe0pEPDHXZV3FcAbJkijJ5oNEnWw==

uc.micro@^2.0.0, uc.micro@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/uc.micro/-/uc.micro-2.1.0.tgz"
  integrity sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unicode-properties@^1.4.0, unicode-properties@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/unicode-properties/-/unicode-properties-1.4.1.tgz"
  integrity sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==
  dependencies:
    base64-js "^1.3.0"
    unicode-trie "^2.0.0"

unicode-trie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/unicode-trie/-/unicode-trie-2.0.0.tgz"
  integrity sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==
  dependencies:
    pako "^0.2.5"
    tiny-inflate "^1.0.0"

unified@^11.0.0:
  version "11.0.5"
  resolved "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz"
  integrity sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==
  dependencies:
    "@types/unist" "^3.0.0"
    bail "^2.0.0"
    devlop "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^4.0.0"
    trough "^2.0.0"
    vfile "^6.0.0"

unist-util-is@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz"
  integrity sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-position@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz"
  integrity sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz"
  integrity sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz"
  integrity sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz"
  integrity sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz"
  integrity sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

update-browserslist-db@^1.1.1:
  version "1.1.3"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  integrity sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  integrity sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==
  dependencies:
    tslib "^2.0.0"

use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  integrity sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1, use-sync-external-store@^1.2.2, use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  integrity sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  integrity sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==
  dependencies:
    base64-arraybuffer "^1.0.2"

uuid@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz"
  integrity sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==

vfile-message@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz"
  integrity sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"

vfile@^6.0.0:
  version "6.0.3"
  resolved "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz"
  integrity sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==
  dependencies:
    "@types/unist" "^3.0.0"
    vfile-message "^4.0.0"

victory-vendor@^36.6.8:
  version "36.9.2"
  resolved "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2.tgz"
  integrity sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

vite-compatible-readable-stream@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/vite-compatible-readable-stream/-/vite-compatible-readable-stream-3.6.1.tgz"
  integrity sha512-t20zYkrSf868+j/p31cRIGN28Phrjm3nRSLR2fyc2tiWi4cZGVdv68yNlwnIINTkMTmPoMiSlc0OadaO7DXZaQ==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

w3c-keyname@^2.2.0:
  version "2.2.8"
  resolved "https://registry.npmjs.org/w3c-keyname/-/w3c-keyname-2.2.8.tgz"
  integrity sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

web-streams-polyfill@4.0.0-beta.3:
  version "4.0.0-beta.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz"
  integrity sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==

web-streams-polyfill@^3.0.3:
  version "3.3.3"
  resolved "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz"
  integrity sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^1.1.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wmf/-/wmf-1.0.2.tgz"
  integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==

word@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/word/-/word-0.3.0.tgz"
  integrity sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-6.0.0.tgz"
  integrity sha512-GmqrO8WJ1NuzJ2DrziEI2o57jKAVIQNf8a18W3nCYU3H7PNWqCCVTeH6/NQE93CIllIgQS98rrmVkYgTX9fFJQ==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^8.17.1, ws@^8.18.0:
  version "8.18.1"
  resolved "https://registry.npmjs.org/ws/-/ws-8.18.1.tgz"
  integrity sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==

xlsx@^0.18.5:
  version "0.18.5"
  resolved "https://registry.npmjs.org/xlsx/-/xlsx-0.18.5.tgz"
  integrity sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz"
  integrity sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==

yaml@^2.3.4:
  version "2.7.1"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.7.1.tgz"
  integrity sha512-10ULxpnOCQXxJvBgxsn9ptjq6uviG/htZKk9veJGhlqn3w/DxQ631zFF+nlQXLwmImeS5amR2dl2U8sg6U9jsQ==

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@^17.7.2:
  version "17.7.2"
  resolved "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yarn@^1.22.22:
  version "1.22.22"
  resolved "https://registry.npmjs.org/yarn/-/yarn-1.22.22.tgz"
  integrity sha512-prL3kGtyG7o9Z9Sv8IPfBNrWTDmXB4Qbes8A9rEzt6wkJV8mUvoirjU0Mp3GGAU06Y0XQyA3/2/RQFVuK7MTfg==

yjs@^13.6.19:
  version "13.6.24"
  resolved "https://registry.npmjs.org/yjs/-/yjs-13.6.24.tgz"
  integrity sha512-xn/pYLTZa3uD1uDG8lpxfLRo5SR/rp0frdASOl2a71aYNvUXdWcLtVL91s2y7j+Q8ppmjZ9H3jsGVgoFMbT2VA==
  dependencies:
    lib0 "^0.2.99"

yoctocolors-cjs@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz"
  integrity sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==

yoga-layout@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/yoga-layout/-/yoga-layout-3.2.1.tgz"
  integrity sha512-0LPOt3AxKqMdFBZA3HBAt/t/8vIKq7VaQYbuA8WxCgung+p9TVyKRYdpvCb80HcdTN2NkbIKbhNwKUfm3tQywQ==

zod-to-json-schema@^3.24.1:
  version "3.24.5"
  resolved "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz"
  integrity sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==

zod@^3.20.2, zod@^3.25.23:
  version "3.25.23"
  resolved "https://registry.npmjs.org/zod/-/zod-3.25.23.tgz"
  integrity sha512-Od2bdMosahjSrSgJtakrwjMDb1zM1A3VIHCPGveZt/3/wlrTWBya2lmEh2OYe4OIu8mPTmmr0gnLHIWQXdtWBg==

zustand@^4.4.0:
  version "4.5.6"
  resolved "https://registry.npmjs.org/zustand/-/zustand-4.5.6.tgz"
  integrity sha512-ibr/n1hBzLLj5Y+yUcU7dYw8p6WnIVzdJbnX+1YpaScvZVF2ziugqHs+LAmHw4lWO9c/zRj+K1ncgWDQuthEdQ==
  dependencies:
    use-sync-external-store "^1.2.2"

zwitch@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz"
  integrity sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==
